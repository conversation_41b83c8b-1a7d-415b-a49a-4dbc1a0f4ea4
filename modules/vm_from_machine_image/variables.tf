variable "project_id" {
  description = "The GCP project ID to deploy resources into"
  type        = string
}

variable "env" {
  type        = string
  description = "value"
}

variable "network" {
  description = "The VPC network self-link"
  type        = string
}

variable "subnetwork" {
  description = "The subnetwork self-link"
  type        = string
}

variable "subnetwork_project" {
  description = "The project ID of the subnetwork (for shared VPC)"
  type        = string
  default     = null
}

variable "vms_from_machine_image" {
  type = map(object({

    zone                     = optional(string, "europe-west2-a")
    machine_type             = optional(string)
    machine_image_name       = string
    machine_image_project_id = optional(string, "")
    deletion_protection      = optional(bool, true)
    labels                   = map(string)
    network_ip               = optional(string)
    nic_type                 = optional(string, null)
    assign_public_ip         = optional(bool, false)
    nat_ip                   = optional(string)
    service_account_email    = optional(string)
    service_account_scopes   = optional(list(string), ["https://www.googleapis.com/auth/cloud-platform"])
    metadata                 = optional(map(string), {})
    attached_disks = optional(list(object({
      name = string
      size = number
      type = string
      mode = string
    })), [])
  }))
  default = {}
}
