# Create VMs from machine images
resource "google_compute_instance_from_machine_image" "vms_from_machine_image" {
  provider = google-beta
  for_each = var.vms_from_machine_image

  name    = each.key
  zone    = each.value.zone
  project = var.project_id

  source_machine_image = length(each.value.machine_image_project_id) > 0 ? "projects/${each.value.machine_image_project_id}/global/machineImages/${each.value.machine_image_name}" : "projects/${var.project_id}/global/machineImages/${each.value.machine_image_name}"

  network_interface {
    network            = var.network
    subnetwork         = var.subnetwork
    subnetwork_project = var.subnetwork_project
    network_ip         = lookup(each.value, "network_ip", null)
    nic_type           = lookup(each.value, "nic_type", null)

    dynamic "access_config" {
      for_each = lookup(each.value, "assign_public_ip", false) ? [1] : []
      content {
        nat_ip = lookup(each.value, "nat_ip", null)
      }
    }
  }

  service_account {
    email  = each.value.service_account_email == null ? "id-${var.env}-vm-default@${var.project_id}.iam.gserviceaccount.com" : each.value.service_account_email
    scopes = lookup(each.value, "service_account_scopes", ["https://www.googleapis.com/auth/cloud-platform"])
  }

  lifecycle {
    ignore_changes = [
      metadata["windows-keys"],
      metadata["ssh-keys"]
    ]
  }

  machine_type        = lookup(each.value, "machine_type", null) //If want to pass diffrent machine type than the Image
  labels              = each.value.labels
  metadata            = each.value.metadata
  deletion_protection = each.value.deletion_protection

}

# Flatten attached disks: vm_name-disk_name → disk object
locals {
  disk_list = flatten([
    for vm_name, vm in var.vms_from_machine_image : [
      for disk in vm.attached_disks : {
        key     = "${vm_name}-${disk.name}"
        vm_name = vm_name
        zone    = vm.zone
        name    = disk.name
        size    = disk.size
        type    = disk.type
        mode    = disk.mode
      }
    ]
  ])

  flattened_disks = tomap({
    for d in local.disk_list :
    d.key => {
      vm_name = d.vm_name
      zone    = d.zone
      name    = d.name
      size    = d.size
      type    = d.type
      mode    = d.mode
    }
  })
}


# Create disks
resource "google_compute_disk" "extra_disks" {
  for_each = local.flattened_disks

  project = var.project_id
  name    = "${each.value.vm_name}-${each.value.name}"
  type    = each.value.type
  zone    = each.value.zone
  size    = each.value.size
}

# Attach disks to correct VM
resource "google_compute_attached_disk" "attach_disks" {
  for_each = local.flattened_disks

  project     = var.project_id
  instance    = google_compute_instance_from_machine_image.vms_from_machine_image[each.value.vm_name].name
  zone        = each.value.zone
  disk        = google_compute_disk.extra_disks[each.key].id
  device_name = google_compute_disk.extra_disks[each.key].name
  mode        = each.value.mode

  depends_on = [google_compute_instance_from_machine_image.vms_from_machine_image]
}
