##########################################################################
#######################   VPC Peering Module  ##########################
##########################################################################


# VPC peering resources
resource "google_compute_network_peering" "vpc_peerings" {
  for_each                            = var.vpc_peering
  name                                = each.key
  network                             = google_compute_network.vpc[each.value.network].self_link
  peer_network                        = google_compute_network.vpc[each.value.peer_network].self_link
  export_custom_routes                = var.export_custom_routes
  import_custom_routes                = var.import_custom_routes
  import_subnet_routes_with_public_ip = true
  export_subnet_routes_with_public_ip = true
}

resource "google_compute_network_peering" "reverse_vpc_peerings" {
  for_each                            = var.vpc_peering
  name                                = "${split("-", each.key)[1]}-${split("-", each.key)[0]}"
  network                             = google_compute_network.vpc[each.value.peer_network].self_link
  peer_network                        = google_compute_network.vpc[each.value.network].self_link
  export_custom_routes                = var.export_custom_routes
  import_custom_routes                = var.import_custom_routes
  import_subnet_routes_with_public_ip = true
  export_subnet_routes_with_public_ip = true
}


