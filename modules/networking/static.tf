##########################################################################
#######################    VPC  custom module   ##########################
##########################################################################


resource "google_compute_network" "vpc" {
  for_each                        = toset(var.environment)
  project                         = var.project_id
  name                            = each.key == "prod" || each.key == "nonprod" ? "hub-${each.key}-01" : "vpc-${each.key}-01"
  auto_create_subnetworks         = var.auto_create_subnetworks
  mtu                             = var.mtu
  routing_mode                    = var.vpc_routing_mode
  delete_default_routes_on_create = var.delete_default_routes_on_create
}

##########################################################################
####################   cloud Router custom module  #######################
##########################################################################

resource "google_compute_router" "cloud_router" {
  for_each = toset(var.environment)
  project  = var.project_id
  name     = "router-${each.key}-${var.region_prefix}-nat"
  region   = var.region
  network  = each.key == "prod" || each.key == "nonprod" ? "hub-${each.key}-01" : "vpc-${each.key}-01"

  bgp {
    asn = var.cloud_router_bgp_asn
  }
  depends_on = [google_compute_network.vpc]
}


##########################################################################
####################   NAT gateway  custom module  #######################
##########################################################################



##################### Reserving External IP  #############################


# First NAT IP
resource "google_compute_address" "ext_ip_address_01" {
  for_each     = toset(var.environment)
  project      = var.project_id
  name         = "ext-ip-nat-${each.key}-${var.region_prefix}-01"
  address_type = "EXTERNAL"
  region       = var.region
}

# Second NAT IP
resource "google_compute_address" "ext_ip_address_02" {
  for_each     = toset(var.environment)
  project      = var.project_id
  name         = "ext-ip-nat-${each.key}-${var.region_prefix}-02"
  address_type = "EXTERNAL"
  region       = var.region
}

# # Dynamic NAT IP creation based on count
# resource "google_compute_address" "ext_ip_addresses" {
#   for_each = {
#     for pair in setproduct(toset(var.environment), range(var.nat_ip_count)) :
#     "${pair[0]}-${pair[1]}" => {
#       environment = pair[0]
#       index       = pair[1] + 1
#     }
#   }
#   project      = var.project_id
#   name         = "ext-ip-nat-${each.value.environment}-${var.region_prefix}-${format("%02d", each.value.index)}"
#   address_type = "EXTERNAL"
#   region       = var.region
# }

######################## Cloud NAT Gateway  ###############################


resource "google_compute_router_nat" "nat_manual" {
  for_each               = toset(var.environment)
  project                = var.project_id
  name                   = "nat-${each.key}-${var.region_prefix}"
  router                 = google_compute_router.cloud_router[each.key].name
  region                 = var.region
  min_ports_per_vm       = 64
  nat_ip_allocate_option = "MANUAL_ONLY"
  nat_ips                = [google_compute_address.ext_ip_address_01[each.key].self_link, google_compute_address.ext_ip_address_02[each.key].self_link]

  source_subnetwork_ip_ranges_to_nat = "ALL_SUBNETWORKS_ALL_IP_RANGES"

  depends_on = [google_compute_router.cloud_router]
}

