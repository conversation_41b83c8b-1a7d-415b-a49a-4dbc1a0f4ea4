output "vpc_id" {
  value = { for env, vpc in google_compute_network.vpc : env => vpc.id }
}

output "self_link" {
  value = { for env, vpc in google_compute_network.vpc : env => vpc.self_link }
}

output "name" {
  value = { for env, vpc in google_compute_network.vpc : env => vpc.name }
}

output "subnet_self_links" {
  description = "The self-links of the created subnets."
  value       = [for s in google_compute_subnetwork.subnet : s.self_link]
}

output "psa_name" {
  value = { for env, addr in google_compute_global_address.psa_address : env => addr.name }
}
