##########################################################################
#######################   subnet custom module  ##########################
##########################################################################



resource "google_compute_subnetwork" "subnet" {
  for_each                 = var.network_config
  name                     = split("/", each.key)[0] == "prod" || split("/", each.key)[0] == "nonprod" ? "snet-${split("/", each.key)[0]}-vm-${split("/", each.key)[1]}" : "snet-${split("/", each.key)[1]}-${split("/", each.key)[0]}-vm-${split("/", each.key)[2]}"
  project                  = var.project_id
  ip_cidr_range            = each.value.primary_cidr_range
  region                   = var.region
  network                  = split("/", each.key)[0] == "prod" || split("/", each.key)[0] == "nonprod" ? google_compute_network.vpc[split("/", each.key)[0]].self_link : google_compute_network.vpc[split("/", each.key)[1]].self_link
  private_ip_google_access = lookup(each.value, "private_ip_google_access")


  # dynamic "secondary_ip_range" {
  #   for_each = each.value.secondary_cidr_ranges
  #   content {
  #     range_name    = secondary_ip_range.value.range_name
  #     ip_cidr_range = secondary_ip_range.value.ip_cidr_range
  #   }
  # }
}

resource "" "name" {
  
}

resource "google_compute_global_address" "psa_address" {
  for_each      = { for k, v in var.network_config : k => v if v.psa_cidr != "" }
  name          = split("/", each.key)[0] == "prod" || split("/", each.key)[0] == "nonprod" ? "psa-${split("/", each.key)[0]}-${split("/", each.key)[1]}" : "psa-${split("/", each.key)[1]}-${split("/", each.key)[0]}-${split("/", each.key)[2]}"
  project       = var.project_id
  purpose       = "VPC_PEERING"
  address_type  = "INTERNAL"
  address       = split("/", each.value.psa_cidr)[0]
  prefix_length = split("/", each.value.psa_cidr)[1]
  network       = split("/", each.key)[0] == "prod" || split("/", each.key)[0] == "nonprod" ? google_compute_network.vpc[split("/", each.key)[0]].self_link : google_compute_network.vpc[split("/", each.key)[1]].self_link
}

############################################
##### Private Service Access/Connection ####
############################################


resource "google_service_networking_connection" "psa_connection" {
  for_each                = { for k, v in var.network_config : k => v if v.psa_cidr != "" }
  network                 = split("/", each.key)[0] == "prod" || split("/", each.key)[0] == "nonprod" ? google_compute_network.vpc[split("/", each.key)[0]].self_link : google_compute_network.vpc[split("/", each.key)[1]].self_link
  service                 = "servicenetworking.googleapis.com"
  reserved_peering_ranges = [google_compute_global_address.psa_address[each.key].name]
}


resource "google_compute_network_peering_routes_config" "peering_routes" {
  for_each = { for k, v in var.network_config : k => v if v.psa_cidr != "" }

  project = var.project_id
  peering = google_service_networking_connection.psa_connection[each.key].peering
  network = split("/", each.key)[0] == "prod" || split("/", each.key)[0] == "nonprod" ? google_compute_network.vpc[split("/", each.key)[0]].self_link : google_compute_network.vpc[split("/", each.key)[1]].self_link

  import_custom_routes = true
  export_custom_routes = true

  depends_on = [google_service_networking_connection.psa_connection]
}


############################
##### Subnet Sharing #######
############################

data "google_project" "service_projects" {
  for_each = toset(distinct(flatten([
    for k, v in var.network_config : [
      split("/", k)[0] == "prod" || split("/", k)[0] == "nonprod"
      ? "mvtech-proj-${split("/", k)[0]}-shared-${split("/", k)[1]}"
      : "mvtech-proj-${split("/", k)[1]}-${split("/", k)[0]}-${split("/", k)[2]}"
    ]
  ])))
  project_id = each.value
}

resource "google_compute_subnetwork_iam_member" "subnet_access" {
  for_each = {
    for pair in flatten([
      for k, v in var.network_config : [
        { subnet_key = k, member = split("/", k)[0] == "prod" || split("/", k)[0] == "nonprod" ? "serviceAccount:${data.google_project.service_projects["mvtech-proj-${split("/", k)[0]}-shared-${split("/", k)[1]}"].number}@cloudservices.gserviceaccount.com" : "serviceAccount:${data.google_project.service_projects["mvtech-proj-${split("/", k)[1]}-${split("/", k)[0]}-${split("/", k)[2]}"].number}@cloudservices.gserviceaccount.com" },
        { subnet_key = k, member = split("/", k)[0] == "prod" || split("/", k)[0] == "nonprod" ? "serviceAccount:id-${split("/", k)[0]}-vm-default@mvtech-proj-${split("/", k)[0]}-shared-${split("/", k)[1]}.iam.gserviceaccount.com" : "serviceAccount:id-${split("/", k)[1]}-vm-default@mvtech-proj-${split("/", k)[1]}-${split("/", k)[0]}-${split("/", k)[2]}.iam.gserviceaccount.com" },
        [for m in lookup(v, "additional_members", []) : { subnet_key = k, member = m }]
      ]
    ]) : "${pair.subnet_key}-${pair.member}" => pair
  }

  project    = var.project_id
  region     = var.region
  subnetwork = google_compute_subnetwork.subnet[each.value.subnet_key].name
  role       = "roles/compute.networkUser"
  member     = each.value.member
}
