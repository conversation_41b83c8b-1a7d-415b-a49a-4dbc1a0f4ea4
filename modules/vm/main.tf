
resource "google_compute_instance" "vm" {
  for_each = var.vms

  name         = each.key
  project      = var.project_id
  zone         = each.value.zone
  machine_type = each.value.machine_type

  resource_policies = [for policy_name in each.value.resource_policies :
    data.terraform_remote_state.resource_policies.outputs.self_links[policy_name]
  ]

  boot_disk {
    initialize_params {
      image = each.value.boot_disk_image
      size  = each.value.boot_disk_size
      type  = each.value.boot_disk_type
    }
    auto_delete = lookup(each.value, "auto_delete", true)
  }

  dynamic "attached_disk" {
    for_each = lookup(each.value, "attached_disks", [])
    content {
      source      = google_compute_disk.attached_disk["${each.key}-${attached_disk.value.name}"].self_link
      device_name = attached_disk.value.name
      mode        = lookup(attached_disk.value, "mode", "READ_WRITE")
    }
  }

  network_interface {
    network            = var.network
    subnetwork         = var.subnetwork
    subnetwork_project = var.subnetwork_project
    network_ip         = lookup(each.value, "network_ip", null)
    nic_type           = lookup(each.value, "nic_type", null)

    dynamic "access_config" {
      for_each = lookup(each.value, "assign_public_ip", false) ? [1] : []
      content {
        nat_ip = lookup(each.value, "nat_ip", null)
      }
    }
  }

  lifecycle {
    ignore_changes = [
      metadata["windows-keys"],
      metadata["ssh-keys"]
    ]
  }

  service_account {
    email  = var.service_account_email
    scopes = lookup(each.value, "service_account_scopes", ["https://www.googleapis.com/auth/cloud-platform"])
  }

  metadata_startup_script = lookup(each.value, "metadata_startup_script", null)
  metadata                = lookup(each.value, "metadata", {})
  tags                    = each.value.tags
  labels                  = each.value.labels
  deletion_protection     = lookup(each.value, "deletion_protection", true)
}



##### Create disks dynamically for each VM's attached_disks #####
resource "google_compute_disk" "attached_disk" {
  for_each = {
    for disk in flatten([
      for vm_name, vm_config in var.vms : [
        for disk in lookup(vm_config, "attached_disks", []) : {
          key       = "${vm_name}-${disk.name}"
          vm_name   = vm_name
          vm_zone   = vm_config.zone
          disk_name = disk.name
          size      = disk.size
          type      = disk.type
        }
      ]
    ]) : disk.key => disk
  }

  name    = "${each.value.vm_name}-${each.value.disk_name}"
  project = var.project_id
  zone    = each.value.vm_zone
  size    = each.value.size
  type    = each.value.type

  labels = {
    vm_name = each.value.vm_name
  }
}
