
variable "vms" {
  description = "Map of VM configurations"
  type = map(object({
    zone                    = string
    machine_type            = string
    boot_disk_size          = number
    boot_disk_type          = string
    boot_disk_image         = string
    resource_policies       = optional(list(string), [])
    nic_type                = optional(string, null)
    assign_public_ip        = optional(bool, false)
    tags                    = optional(list(string), [])
    labels                  = optional(map(string), {})
    metadata_startup_script = optional(string)
    metadata                = optional(map(string), {})
    auto_delete             = optional(bool, true)
    deletion_protection     = optional(bool, true)
    network_ip              = optional(string)
    nat_ip                  = optional(string)
    service_account_scopes  = optional(list(string), ["https://www.googleapis.com/auth/cloud-platform"])
    attached_disks = optional(list(object({
      name = string
      mode = optional(string, "READ_WRITE")
      size = optional(number, 50)
      type = optional(string, "pd-standard")
    })), [])
  }))
  default = {}
}

variable "project_id" {
  description = "The GCP project ID"
  type        = string
}

variable "network" {
  description = "The VPC network self-link"
  type        = string
}

variable "subnetwork" {
  description = "The subnetwork self-link"
  type        = string
}

variable "subnetwork_project" {
  description = "The project ID of the subnetwork (for shared VPC)"
  type        = string
  default     = null
}

variable "service_account_email" {
  description = "The service account email to attach to VMs"
  type        = string
}
