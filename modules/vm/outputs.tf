output "vm_instances" {
  description = "Map of VM instances created"
  value       = google_compute_instance.vm
}

output "vm_names" {
  description = "List of VM names created"
  value       = [for vm in google_compute_instance.vm : vm.name]
}

output "vm_self_links" {
  description = "Map of VM self-links"
  value       = { for name, vm in google_compute_instance.vm : name => vm.self_link }
}

output "vm_internal_ips" {
  description = "Map of VM internal IP addresses"
  value       = { for name, vm in google_compute_instance.vm : name => vm.network_interface[0].network_ip }
}

output "vm_external_ips" {
  description = "Map of VM external IP addresses (if assigned)"
  value = {
    for name, vm in google_compute_instance.vm : name => (
      length(vm.network_interface[0].access_config) > 0 ?
      vm.network_interface[0].access_config[0].nat_ip : null
    )
  }
}
