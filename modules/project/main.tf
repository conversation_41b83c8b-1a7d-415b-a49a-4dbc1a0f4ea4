###########################
####### GCP Project #######
###########################

resource "google_project" "project" {
  name                = var.project_name
  project_id          = "mvtech-${var.project_name}"
  org_id              = var.org_id
  folder_id           = var.folder_id
  billing_account     = var.billing_account
  deletion_policy     = var.deletion_policy
  auto_create_network = var.auto_create_network
  labels              = var.labels
}

#########################
##### Host Project ######
#########################

resource "google_compute_shared_vpc_host_project" "host_project" {
  count   = var.enable_host_project ? 1 : 0
  project = google_project.project.project_id

  depends_on = [
    google_project.project
  ]
}

######################################
##### Attaching Service Project ######
######################################

resource "google_compute_shared_vpc_service_project" "shared_vpc_attachment" {
  provider = google-beta

  count           = length(var.host_project_id) > 0 ? 1 : 0
  host_project    = var.host_project_id
  service_project = google_project.project.project_id
  depends_on      = [google_project.project, google_project_service.enable_apis]
}

############################
###### Enabling APIs #######
############################

resource "google_project_service" "enable_apis" {
  project                    = google_project.project.project_id
  for_each                   = toset(var.enable_apis) != null ? toset(var.enable_apis) : toset([])
  service                    = each.value
  disable_on_destroy         = var.disable_on_destroy
  disable_dependent_services = var.disable_dependent_services

  depends_on = [
    google_project.project
  ]
}

################################################
##### Deprivilege default service accounts #####
################################################

resource "google_project_default_service_accounts" "project" {
  count   = var.deprivilege_sa ? 1 : 0
  project = google_project.project.project_id
  action  = "DEPRIVILEGE"

  depends_on = [
    google_project.project
  ]
}

###########################################################
##### New Service Account Creation for Compute Engine #####
###########################################################

resource "google_service_account" "sa" {
  count        = var.create_sa ? 1 : 0
  project      = google_project.project.project_id
  account_id   = "id-${var.environment}-vm-default"
  display_name = "Compute Engine Service Account"
}

resource "google_service_account" "gcs_sa" {
  count        = var.create_sa ? 1 : 0
  project      = google_project.project.project_id
  account_id   = "id-${var.environment}-vm-gcs"
  display_name = "Compute Engine Service Account"
  description = "Compute Engine SA with access to GCS"
}