variable "project_name" {
  type        = string
  description = "Project ID"
  default     = ""
}

variable "environment" {
  type        = string
  description = "Environment name"
  default     = ""
}

variable "folder_id" {
  type        = string
  description = "The numeric ID of the folder this project should be created under"
  default     = null
}

variable "org_id" {
  type        = string
  description = "The numeric ID of the organization this project belongs to"
  default     = null
}

variable "billing_account" {
  type        = string
  description = "The alphanumeric ID of the billing account this project belongs to"
  default     = ""
}

variable "deletion_policy" {
  type        = string
  description = "If Prevent, the Terraform resource can be deleted without deleting the Project via the Google API"
  default     = "PREVENT"
}

variable "auto_create_network" {
  type        = bool
  description = "Controls whether the 'default' network exists on the project"
  default     = false
}

variable "labels" {
  type        = map(string)
  description = "A set of key/value label pairs to assign to the project"
  default     = null
}

# Marking Host Project variables

variable "enable_host_project" {
  type        = bool
  description = "Whether to enable as host project"
  default     = false
}

variable "host_project_id" {
  type        = string
  description = "ID of the host project to attach the service project with"
  default     = ""
}

# Enabling APIs related variables

variable "enable_apis" {
  type        = list(string)
  description = "List of APIs that needs to be enabled on the project."
  default     = []
}

variable "disable_on_destroy" {
  type        = bool
  description = "If true, disable the service when the Terraform resource is destroyed"
  default     = true
}

variable "disable_dependent_services" {
  type        = bool
  description = "If true, services that are enabled and which depend on this service should also be disabled when this service is destroyed"
  default     = true
}

# Deprivilage the service account 

variable "deprivilege_sa" {
  type        = bool
  description = "Mark as true if want to deprivilege default service accounts."
  default     = true
}

variable "create_sa" {
  type        = bool
  description = "Set to true to create a default compute service account."
  default     = false
}
