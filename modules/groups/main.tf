#################################
##### Cloud Identity Groups #####
#################################

##### Group Creation
resource "googleworkspace_group" "group" {
  email = var.group_email
  name  = split("@", var.group_email)[0]
}



##### Group Settings
resource "googleworkspace_group_settings" "group_setting" {
  email                  = var.group_email
  allow_external_members = var.allow_external_members
  who_can_join           = "INVITED_CAN_JOIN"
  who_can_contact_owner  = "ALL_IN_DOMAIN_CAN_CONTACT"
  who_can_post_message   = "ALL_IN_DOMAIN_CAN_POST"
  who_can_view_group     = "ALL_IN_DOMAIN_CAN_VIEW"
  primary_language       = "en_US"
  who_can_assist_content = "OWNERS_AND_MANAGERS"

  depends_on = [googleworkspace_group.group]
}



##### Adding Members to the group
resource "googleworkspace_group_member" "name" {
  for_each = toset(var.members)
  group_id = googleworkspace_group.group.email
  email    = split(":", each.value)[1]
  type     = upper(split(":", each.value)[0]) == "SERVICEACCOUNT" ? "USER" : upper(split(":", each.value)[0])

  depends_on = [googleworkspace_group_settings.group_setting]
}
