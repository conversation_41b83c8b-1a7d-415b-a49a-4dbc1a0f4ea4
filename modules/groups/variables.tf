#####################
##### Variables #####
#####################

variable "group_email" {
  type        = string
  description = "email id for the group"
}

variable "members" {
  type        = list(string)
  default     = null
  description = "List of members to add to the group, in 'type:email' format (e.g., 'user:<EMAIL>'). Valid types are USER and GROUP. SERVICEACCOUNT is also accepted and treated as USER."
}

variable "allow_external_members" {
  type        = bool
  default     = false
  description = "Set true if admin wants to add external users outside from ORG"
}