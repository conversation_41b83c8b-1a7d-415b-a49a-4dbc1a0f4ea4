locals {
  # Generate unique keys that include scope identifiers to prevent conflicts
  member_role_scope_pairs = flatten([
    for m in var.members_roles : [
      for r in m.roles : {
        key = join("-", compact([
          var.parent == "project" ? var.project : null,
          var.parent == "folder" ? var.folder_id : null,
          var.parent == "organization" ? var.org_id : null,
          replace(m.member, "/[^a-zA-Z0-9]/", "-"),
          replace(r, "/[^a-zA-Z0-9]/", "-")
        ]))
        member = m.member
        role   = r
      }
    ]
  ])
}

# Project IAM
resource "google_project_iam_member" "project_iam" {
  for_each = var.parent == "project" ? {
    for mapping in local.member_role_scope_pairs : mapping.key => mapping
  } : {}

  project = var.project
  member  = each.value.member
  role    = each.value.role
}

# Folder IAM
resource "google_folder_iam_member" "folder_iam" {
  for_each = var.parent == "folder" ? {
    for mapping in local.member_role_scope_pairs : mapping.key => mapping
  } : {}

  folder = var.folder_id
  member = each.value.member
  role   = each.value.role
}

# Organization IAM
resource "google_organization_iam_member" "org_iam" {
  for_each = var.parent == "organization" ? {
    for mapping in local.member_role_scope_pairs : mapping.key => mapping
  } : {}

  org_id = var.org_id
  member = each.value.member
  role   = each.value.role
}
