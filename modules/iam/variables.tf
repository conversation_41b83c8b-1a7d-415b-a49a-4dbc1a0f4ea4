variable "parent" {
  description = "The IAM parent type: project, folder, or organization"
  type        = string
  validation {
    condition     = contains(["project", "folder", "organization"], var.parent)
    error_message = "custom_role_parent must be one of: project, folder, organization"
  }
}

variable "project" {
  description = "Project ID (required if parent = project)"
  type        = string
  default     = null
}

variable "folder_id" {
  description = "Folder ID (required if parent = folder)"
  type        = string
  default     = null
}

variable "org_id" {
  description = "Organization ID (required if parent = organization)"
  type        = string
  default     = null
}

variable "members_roles" {
  description = "List of members and their roles"
  default = null
  type = map(object({
  member = string
  roles  = list(string)
  }))
}
