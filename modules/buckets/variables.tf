variable "project_id" {
  type        = string
  description = "Project ID"
  default     = ""
}

variable "bucket_configs" {
  type = map(object({
    location                     = string
    force_destroy                = optional(bool, false)
    storage_class                = optional(string, "STANDARD")
    labels                       = optional(map(string), {})
    versioning                   = optional(bool, false)
    public_access_prevention     = optional(string, "enforced")
    uniform_bucket_level_access  = optional(bool, true)
    autoclass_enable             = optional(bool, false)
    soft_delete_retention_period = optional(number, 0)
    lifecycle_rule = optional(list(object({
      age                        = optional(number)
      num_newer_versions         = optional(number)
      days_since_noncurrent_time = optional(number)
      action                     = string
      storage_class              = optional(string)
    })), [])
    retention_policy = optional(list(object({
      retention_period = optional(number)
    })), [])
    cors = optional(list(object({
      origin          = optional(string)
      method          = optional(string)
      response_header = optional(string)
      max_age_seconds = optional(number)
    })), [])
    website = optional(map(any), {})
    logging = optional(list(object({
      log_bucket        = string
      log_object_prefix = optional(string)
    })), [])
    iam_members = optional(list(object({
      role    = string
      members = list(string)
    })), [])
  }))
  description = "Map of bucket configurations"
  default     = {}
}
