
#########################################
################ GCS Bucket #############
#########################################

resource "google_storage_bucket" "bucket" {
  for_each                    = var.bucket_configs
  project                     = var.project_id
  name                        = each.key
  location                    = each.value.location
  force_destroy               = each.value.force_destroy
  storage_class               = each.value.autoclass_enable ? null : each.value.storage_class
  public_access_prevention    = each.value.public_access_prevention
  uniform_bucket_level_access = each.value.uniform_bucket_level_access
  labels                      = each.value.labels

  versioning {
    enabled = each.value.versioning
  }

  autoclass {
    enabled = each.value.autoclass_enable
  }

  soft_delete_policy {
    retention_duration_seconds = each.value.soft_delete_retention_period
  }

  dynamic "lifecycle_rule" {
    for_each = each.value.lifecycle_rule
    content {
      condition {
        age                        = lifecycle_rule.value.age
        num_newer_versions         = lifecycle_rule.value.num_newer_versions
        days_since_noncurrent_time = lifecycle_rule.value.days_since_noncurrent_time
      }
      action {
        type          = lifecycle_rule.value.action
        storage_class = lifecycle_rule.value.storage_class
      }
    }
  }

  dynamic "retention_policy" {
    for_each = each.value.retention_policy
    content {
      retention_period = retention_policy.value.retention_period
    }
  }

  dynamic "cors" {
    for_each = each.value.cors
    content {
      origin          = lookup(cors.value, "origin", null)
      method          = lookup(cors.value, "method", null)
      response_header = lookup(cors.value, "response_header", null)
      max_age_seconds = lookup(cors.value, "max_age_seconds", null)
    }
  }

  dynamic "website" {
    for_each = length(keys(each.value.website)) == 0 ? toset([]) : toset([each.value.website])
    content {
      main_page_suffix = lookup(website.value, "main_page_suffix", null)
      not_found_page   = lookup(website.value, "not_found_page", null)
    }
  }

  dynamic "logging" {
    for_each = each.value.logging
    content {
      log_bucket        = logging.value.log_bucket
      log_object_prefix = logging.value.log_object_prefix
    }
  }
}

#########################################
############## Bucket IAM ###############
#########################################

resource "google_storage_bucket_iam_member" "bucket_iam_members" {
  for_each = {
    for iam in flatten([
      for bucket_name, bucket_config in var.bucket_configs : [
        for role_config in lookup(bucket_config, "iam_members", []) : [
          for member in role_config.members : {
            key    = "${bucket_name}-${role_config.role}-${member}"
            bucket = bucket_name
            role   = role_config.role
            member = member
          }
        ]
      ]
    ]) : iam.key => iam
  }

  bucket = google_storage_bucket.bucket[each.value.bucket].name
  role   = each.value.role
  member = each.value.member

  depends_on = [google_storage_bucket.bucket]
}
