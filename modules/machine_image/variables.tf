#####################
##### Variables #####
#####################

variable "project_id" {
  type        = string
  description = "The Project ID"
  default     = null
}

variable "create_machine_image" {
  type        = bool
  description = "Whether to create a machine image from the source instance"
  default     = false
}

variable "machine_images" {
  type = map(object({
    source_instance    = string
    zone               = optional(string, "europe-west2-a")
  }))
  default = {}
}

