variable "create_ops_agent_policy" {
  type        = bool
  description = "Whether to create the ops agent policy."
  default     = false
}

variable "project_id" {
  type        = string
  description = "Project ID"
  default     = null
}

variable "labels" {
  type        = map(string)
  description = "Labels which instances should have to apply this policy"
  default     = {}
}

variable "zone" {
  type        = string
  description = "Zone for the Zonal OS Policy"
  default     = "europe-west2-a"
}

variable "policy_id" {
  type        = string
  description = "Assignment ID for the OS policy"
  default     = null
}
