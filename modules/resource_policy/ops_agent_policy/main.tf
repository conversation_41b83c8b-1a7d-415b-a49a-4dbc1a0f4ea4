#########################################################################################
##### This is using a google official module to install ops agent for a fleet of VM #####
#########################################################################################

module "ops_agent_policy" {
  source          = "github.com/terraform-google-modules/terraform-google-cloud-operations/modules/ops-agent-policy"

  count           = var.create_ops_agent_policy ? 1 : 0
  project         = var.project_id
  zone            = var.zone
  assignment_id   = var.policy_id
  agents_rule = {
    package_state = "installed"
    version = "latest"
  }
  instance_filter = {
    inclusion_labels = [{
      labels = var.labels
    }]
  }
}


