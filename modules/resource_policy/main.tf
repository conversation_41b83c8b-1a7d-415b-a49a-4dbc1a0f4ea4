###########################
##### Resource Policy #####
###########################

resource "google_compute_resource_policy" "policy" {
  name        = var.policy_name
  project     = var.project_id
  region      = var.region
  description = var.description

  dynamic "instance_schedule_policy" {
    for_each = var.instance_schedule_policy != null ? [var.instance_schedule_policy] : []
    content {
      vm_start_schedule {
        schedule = lookup(instance_schedule_policy.value, "vm_start_schedule", null)
      }
      vm_stop_schedule {
        schedule = lookup(instance_schedule_policy.value, "vm_stop_schedule", null)
      }
      time_zone = lookup(instance_schedule_policy.value, "time_zone", null)
    }
  }

  dynamic "snapshot_schedule_policy" {
    for_each = var.snapshot_schedule_policy != null ? [var.snapshot_schedule_policy] : []
    content {
      # daily_schedule
      schedule {
        daily_schedule {
          days_in_cycle = snapshot_schedule_policy.value.schedule.daily_schedule.days_in_cycle
          start_time    = snapshot_schedule_policy.value.schedule.daily_schedule.start_time
        }
      }

      # snapshot_properties
      snapshot_properties {
        guest_flush       = snapshot_schedule_policy.value.snapshot_properties.guest_flush
        storage_locations = snapshot_schedule_policy.value.snapshot_properties.storage_locations
      } 

      retention_policy {
        max_retention_days = snapshot_schedule_policy.value.retention_policy.max_retention_days
        on_source_disk_delete = snapshot_schedule_policy.value.retention_policy.on_source_disk_delete
      }
    }
  }


}
