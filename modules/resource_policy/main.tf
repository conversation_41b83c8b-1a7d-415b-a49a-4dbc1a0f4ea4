###########################
##### Resource Policy #####
###########################

resource "google_compute_resource_policy" "policy" {
  name        = var.policy_name
  project     = var.project_id
  region      = var.region
  description = var.description

  dynamic "instance_schedule_policy" {
    for_each = var.instance_schedule_policy != null ? [var.instance_schedule_policy] : []
    content {
      vm_start_schedule {
        schedule = lookup(instance_schedule_policy.value, "vm_start_schedule", null)
      }
      vm_stop_schedule {
        schedule = lookup(instance_schedule_policy.value, "vm_stop_schedule", null)
      }
      time_zone = lookup(instance_schedule_policy.value, "time_zone", null)
    }
  }

  # dynamic "snapshot_schedule_policy" {
  #   for_each = var.snapshot_schedule_policy != null ? [var.snapshot_schedule_policy] : []
  #   content {
  #     schedule {
        
  #     }
  #   }
  
  # }
}
