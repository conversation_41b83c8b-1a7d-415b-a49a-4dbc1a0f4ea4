variable "project_id" {
  type        = string
  description = "Project ID"
  default     = null
}

variable "policy_name" {
  description = "The name of the resource policy."
  type        = string
}

variable "region" {
  description = "The region where the resource policy will be created."
  type        = string
  default     = "europe-west2"
}

variable "description" {
  description = "A description of the resource policy."
  type        = string
  default     = null
}

variable "instance_schedule_policy" {
  description = "Configuration for an instance schedule policy. Set to null if not creating this policy type."
  type = object({
    vm_start_schedule = string
    vm_stop_schedule  = string
    time_zone         = string
  })
  default = null
}