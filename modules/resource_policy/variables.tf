variable "project_id" {
  type        = string
  description = "Project ID"
  default     = null
}

variable "policy_name" {
  description = "The name of the resource policy."
  type        = string
}

variable "region" {
  description = "The region where the resource policy will be created."
  type        = string
  default     = "europe-west2"
}

variable "description" {
  description = "A description of the resource policy."
  type        = string
  default     = null
}

variable "instance_schedule_policy" {
  description = "Configuration for an instance schedule policy. Set to null if not creating this policy type."
  type = object({
    vm_start_schedule = string
    vm_stop_schedule  = string
    time_zone         = string
  })
  default = null
}

variable "snapshot_schedule_policy" {
  description = "Configuration for snapshot schedule policy. Set to null if not creating this policy type."
  type = object({
    schedule = object({
      daily_schedule = object({
        days_in_cycle = number
        start_time    = string # HH:MM in UTC
      })
    })
    snapshot_properties = object({
      guest_flush       = bool
      storage_locations = list(string)
    })
    retention_policy = object({
      max_retention_days    = number
      on_source_disk_delete = optional(string) # "APPLY_RETENTION_POLICY" or "KEEP_AUTO_SNAPSHOTS"
    })
  })
  default = null
}