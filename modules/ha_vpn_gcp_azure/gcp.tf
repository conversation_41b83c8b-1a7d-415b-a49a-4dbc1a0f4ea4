
# GCP existing network
data "google_compute_network" "gcp_vpn_network" {
  name = var.gcp_vpc_name
  project = var.gcp_project_id
}

resource "google_compute_router" "vpn_router" {
  name    = "cr-${var.purpose}-azure-to-gcp-ew2-ha-vpn-01"
  network = var.gcp_vpc_name
  project = var.gcp_project_id
  region = var.gcp_region

  bgp {
    asn               = var.gcp_bgp_asn
    advertise_mode    = "CUSTOM"
    advertised_groups = ["ALL_SUBNETS"]
  }
}

# GCP HA VPN gateway
resource "google_compute_ha_vpn_gateway" "target_gateway" {
  name    = "vng-gcp-to-azure-${var.purpose}-ha-vpn-01"
  network = var.gcp_vpc_name
  project = var.gcp_project_id
  region = var.gcp_region
}

resource "google_compute_external_vpn_gateway" "azure_gateway" {
  name            = "vng-azure-to-gcp-${var.purpose}-vpn-01"
  redundancy_type = "SINGLE_IP_INTERNALLY_REDUNDANT"
  description     = "Azure VPN Gateway (single IP, redundant)"
  project         = var.gcp_project_id

  interface {
    id         = 0
    ip_address = var.azure_vpn_gateway_public_ip_1
  }
}

resource "google_compute_vpn_tunnel" "tunnel_1" {
  name                            = "tunnel-${var.purpose}-gcp-to-azure-ha-vpn-01"
  vpn_gateway                     = google_compute_ha_vpn_gateway.target_gateway.self_link
  vpn_gateway_interface           = 0
  peer_external_gateway           = google_compute_external_vpn_gateway.azure_gateway.self_link
  peer_external_gateway_interface = 0
  shared_secret                   = var.shared_key
  router                          = google_compute_router.vpn_router.name
  ike_version                     = 2
  project                         = var.gcp_project_id
  region                          = var.gcp_region
}

resource "google_compute_vpn_tunnel" "tunnel_2" {
  name                            = "tunnel-${var.purpose}-gcp-to-azureha-vpn-02"
  vpn_gateway                     = google_compute_ha_vpn_gateway.target_gateway.self_link
  vpn_gateway_interface           = 1   # different local interface
  peer_external_gateway           = google_compute_external_vpn_gateway.azure_gateway.self_link
  peer_external_gateway_interface = 0   # same Azure interface
  shared_secret                   = var.shared_key
  router                          = google_compute_router.vpn_router.name
  ike_version                     = 2
  project                         = var.gcp_project_id
  region                          = var.gcp_region
}


resource "google_compute_router_interface" "router1_interface1" {
  name       = "interface-${var.purpose}-01"
  router     = google_compute_router.vpn_router.name
  ip_range   = var.gcp_router_interface1_ip_range
  vpn_tunnel = google_compute_vpn_tunnel.tunnel_1.name
  project = var.gcp_project_id
  region = var.gcp_region
}

resource "google_compute_router_peer" "router1_peer1" {
  name                      = "bgp-peer-${var.purpose}-gcp-to-azure-01"
  router                    = google_compute_router.vpn_router.name
  peer_ip_address           = var.gcp_router_peer_ip_address1
  peer_asn                  = var.azure_bgp_asn
  advertised_route_priority = 100
  interface                 = google_compute_router_interface.router1_interface1.name
  project = var.gcp_project_id
  region = var.gcp_region
}

resource "google_compute_router_interface" "router1_interface2" {
  name       = "interface-${var.purpose}-02"
  router     = google_compute_router.vpn_router.name
  ip_range   = var.gcp_router_interface2_ip_range
  vpn_tunnel = google_compute_vpn_tunnel.tunnel_2.name
  project = var.gcp_project_id
  region = var.gcp_region
}

resource "google_compute_router_peer" "router1_peer2" {
  name                      = "bgp-peer-${var.purpose}-gcp-to-azure-02"
  router                    = google_compute_router.vpn_router.name
  peer_ip_address           = var.gcp_router_peer_ip_address2
  peer_asn                  = var.azure_bgp_asn
  advertised_route_priority = 100
  interface                 = google_compute_router_interface.router1_interface2.name
  project = var.gcp_project_id
  region = var.gcp_region
}