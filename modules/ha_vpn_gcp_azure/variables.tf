# Azure variables

variable "azure_vpn_gateway_public_ip_1" {
  type = string
  default = ""
}

# variable "azure_vpn_gateway_public_ip_2" {
#   type = string
# }

# GCP variables
variable "gcp_project_id" {
  description = "The GCP project ID."
  type        = string
}

variable "gcp_region" {
  description = "The GCP region."
  type        = string
}

variable "gcp_vpc_name" {
  description = "The GCP VPC name."
  type        = string
  default = ""
}

variable "gcp_bgp_asn" {
  description = "The GCP VPC Router ASN"
  type        = string
  default     = "64512"
}

variable "azure_bgp_asn" {
  description = "The Azure Router ASN"
  type        = string
  default     = "65511"
}

variable "gcp_router_interface1_ip_range" {
  type        = string
  description = "The IP address and range of the interface."
  default = ""
}

variable "gcp_router_interface2_ip_range" {
  type        = string
  description = "The IP address and range of the interface."
}

variable "gcp_router_peer_ip_address1" {
  type        = string
  description = "IP address of the BGP interface outside Google Cloud Platform."
  default = ""
}

variable "gcp_router_peer_ip_address2" {
  type        = string
  description = "IP address of the BGP interface outside Google Cloud Platform."
}

# Common variables

# variable "prefix" {
#   type        = string
#   description = "The prefix for the naming convention."
#   default     = null
# }

variable "purpose" {
  type        = string
  description = "The GCP environment in which the deployment will be done"
  default     = null
}

variable "azure_env" {
  type        = string
  description = "The Azure environment in which the deployment will be done"
  default     = null
}

variable "shared_key" {
  type = string
  default = ""
}