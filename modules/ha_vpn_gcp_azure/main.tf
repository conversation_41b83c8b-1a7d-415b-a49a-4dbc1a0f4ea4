# # -----------------------------
# # GCP existing network
# # -----------------------------
# data "google_compute_network" "gcp_vpn_network" {
#   name    = var.gcp_vpc_name
#   project = var.gcp_project_id
# }

# # -----------------------------
# # Cloud Router (for BGP)
# # -----------------------------
# resource "google_compute_router" "vpn_router" {
#   name    = "cr-${var.purpose}-ew1-vpn-01"
#   network = var.gcp_vpc_name
#   project = var.gcp_project_id
#   region = var.gcp_region

#   bgp {
#     asn               = var.gcp_bgp_asn
#     advertise_mode    = "CUSTOM"
#     advertised_groups = ["ALL_SUBNETS"]
#   }
# }

# # -----------------------------
# # Classic VPN Gateway (Target VPN Gateway)
# # -----------------------------
# resource "google_compute_vpn_gateway" "target_gateway" {
#   name    = "vng-gcp-to-azure-${var.purpose}-vpn-01"
#   network = var.gcp_vpc_name
#   project = var.gcp_project_id
#   # region is inherited from provider if set; otherwise specify:
#   region = var.gcp_region
# }

# # -----------------------------
# # Static External IP for Classic VPN Gateway
# # -----------------------------
# resource "google_compute_address" "vpn_ip" {
#   name    = "addr-gcp-to-azure-${var.purpose}-vpn-01"
#   region  = var.gcp_region
#   project = var.gcp_project_id
# }

# # -----------------------------
# # Forwarding rules for Classic VPN (ESP / UDP 500 / UDP 4500)
# # All point to the Target VPN Gateway
# # -----------------------------
# resource "google_compute_forwarding_rule" "fr_esp" {
#   name        = "fr-esp-gcp-to-azure-${var.purpose}-vpn-01"
#   ip_protocol = "ESP"
#   ip_address  = google_compute_address.vpn_ip.address
#   target      = google_compute_vpn_gateway.target_gateway.self_link
#   region      = var.gcp_region
#   project     = var.gcp_project_id
# }

# resource "google_compute_forwarding_rule" "fr_udp500" {
#   name        = "fr-udp500-gcp-to-azure-${var.purpose}-vpn-01"
#   ip_protocol = "UDP"
#   port_range  = "500"
#   ip_address  = google_compute_address.vpn_ip.address
#   target      = google_compute_vpn_gateway.target_gateway.self_link
#   region      = var.gcp_region
#   project     = var.gcp_project_id
# }

# resource "google_compute_forwarding_rule" "fr_udp4500" {
#   name        = "fr-udp4500-gcp-to-azure-${var.purpose}-vpn-01"
#   ip_protocol = "UDP"
#   port_range  = "4500"
#   ip_address  = google_compute_address.vpn_ip.address
#   target      = google_compute_vpn_gateway.target_gateway.self_link
#   region      = var.gcp_region
#   project     = var.gcp_project_id
# }

# # -----------------------------
# # Classic VPN Tunnel (Route-based, IKEv2, BGP via Cloud Router)
# # NOTE: Classic uses target_vpn_gateway + peer_ip
# # -----------------------------
# resource "google_compute_vpn_tunnel" "tunnel_1" {
#   name               = "tunnel-${var.purpose}-vpn-01"
#   target_vpn_gateway = google_compute_vpn_gateway.target_gateway.self_link
#   peer_ip            = var.azure_vpn_gateway_public_ip_1
#   shared_secret      = var.shared_key
#   ike_version        = 2
#   router             = google_compute_router.vpn_router.name
#   project            = var.gcp_project_id
#   region             = var.gcp_region

#   # Optionally pin traffic selectors if needed by Azure policy:
#   # local_traffic_selector  = var.local_traffic_selector   # list(string)
#   # remote_traffic_selector = var.remote_traffic_selector  # list(string)

#   depends_on = [
#     google_compute_forwarding_rule.fr_esp,
#     google_compute_forwarding_rule.fr_udp500,
#     google_compute_forwarding_rule.fr_udp4500
#   ]
# }

# # If/when you add a second tunnel to another Azure public IP on the same Classic gateway:
# # - Reuse the same Target VPN Gateway & forwarding rules (same public IP).
# # - Just create another google_compute_vpn_tunnel with peer_ip = var.azure_vpn_gateway_public_ip_2
# # resource "google_compute_vpn_tunnel" "tunnel_2" {
# #   name               = "tunnel-${var.purpose}-vpn-02"
# #   target_vpn_gateway = google_compute_vpn_gateway.target_gateway.self_link
# #   peer_ip            = var.azure_vpn_gateway_public_ip_2
# #   shared_secret      = var.shared_key
# #   ike_version        = 2
# #   router             = google_compute_router.vpn_router.name
# # }

# # -----------------------------
# # Cloud Router interfaces & BGP peers (for tunnel_1)
# # -----------------------------
# resource "google_compute_router_interface" "router1_interface1" {
#   name       = "interface-${var.purpose}-01"
#   router     = google_compute_router.vpn_router.name
#   ip_range   = var.gcp_router_interface1_ip_range # e.g., "***********/30"
#   vpn_tunnel = google_compute_vpn_tunnel.tunnel_1.name
#   project    = var.gcp_project_id
#   region = var.gcp_region
# }

# resource "google_compute_router_peer" "router1_peer1" {
#   name                      = "bgp-peer-${var.purpose}-gcp-azure-01"
#   router                    = google_compute_router.vpn_router.name
#   interface                 = google_compute_router_interface.router1_interface1.name
#   peer_ip_address           = var.gcp_router_peer_ip_address1 # e.g., "***********"
#   peer_asn                  = var.azure_bgp_asn
#   advertised_route_priority = 100
#   project                   = var.gcp_project_id
#   region                    = var.gcp_region
# }

# # For a second tunnel with second BGP session:
# # resource "google_compute_router_interface" "router1_interface2" {
# #   name       = "interface-${var.purpose}-02"
# #   router     = google_compute_router.vpn_router.name
# #   ip_range   = var.gcp_router_interface2_ip_range
# #   vpn_tunnel = google_compute_vpn_tunnel.tunnel_2.name
# # }
# #
# # resource "google_compute_router_peer" "router1_peer2" {
# #   name                      = "bgp-peer-${var.purpose}-gcp-azure-02"
# #   router                    = google_compute_router.vpn_router.name
# #   interface                 = google_compute_router_interface.router1_interface2.name
# #   peer_ip_address           = var.gcp_router_peer_ip_address2
# #   peer_asn                  = var.azure_bgp_asn
# #   advertised_route_priority = 100
# # }


