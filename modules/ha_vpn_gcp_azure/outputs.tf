
output "gcp_vpn_gateway_ip1" {
  value       = google_compute_ha_vpn_gateway.target_gateway.vpn_interfaces.0.ip_address
  description = "GCP VPN Gateway IP address for the first VPN tunnel."
}

output "gcp_vpn_gateway_ip2" {
  value       = google_compute_ha_vpn_gateway.target_gateway.vpn_interfaces.1.ip_address
  description = "GCP VPN Gateway IP address for the second VPN tunnel."
}

output "gcp_peer_ip1" {
  value       = google_compute_router_peer.router1_peer1.ip_address
  description = "GCP BGP peer IP address for the first VPN tunnel."
}

output "gcp_peer_ip2" {
  value       = google_compute_router_peer.router1_peer2.ip_address
  description = "GCP BGP peer IP address for the second VPN tunnel."
}


# # -----------------------------
# # Outputs (Classic VPN IP)
# # -----------------------------
# output "gcp_vpn_gateway_ip1" {
#   value       = google_compute_address.vpn_ip.address
#   description = "Classic VPN Gateway public IP."
# }