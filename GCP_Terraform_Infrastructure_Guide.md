# GCP Terraform Infrastructure Guide

## Overview

This document provides a comprehensive guide to the Google Cloud Platform (GCP) Terraform infrastructure setup for Mutual Vision Technology. The infrastructure follows a modular, scalable approach with clear separation between environments and components.

## Architecture Overview

### Organizational Structure

```
Organization (mutualvision.co.uk)
├── fldr-mvt (Root Folder)
    ├── fldr-automation (Automation Resources)
    ├── fldr-shared (Shared Services)
    ├── fldr-nonprod (Non-Production)
    │   ├── fldr-dev
    │   ├── fldr-qa
    │   ├── fldr-demo
    │   └── fldr-devops
    └── fldr-prod (Production)
        ├── fldr-cuat
        └── fldr-live
```

### Project Naming Convention

- **Host Projects**: `mvtech-proj-{env}-host-01`
- **Service Projects**: `mvtech-proj-{env}-{client}-{number}`
- **Shared Projects**: `mvtech-proj-{env}-shared-01`

Examples:
- `mvtech-proj-nonprod-host-01` (NonProd Hub)
- `mvtech-proj-dev-sun-01` (Dev environment for client "sun")
- `mvtech-proj-prod-shared-01` (Prod shared services)

## Directory Structure

```
├── Components/          # Terraform configurations
│   ├── Internal/        # Internal MVT infrastructure
│   └── Customer/        # Customer-specific resources
├── Environments/        # Environment-specific variables
│   ├── Internal/        # Internal environment configs
│   └── Customer/        # Customer environment configs
├── modules/             # Reusable Terraform modules
├── Pipelines/           # CI/CD pipeline definitions
└── templates/           # Pipeline templates
```

## Core Components

### 1. Folders (`Components/Internal/folders/`)
**Purpose**: Creates the organizational folder hierarchy in GCP.

**Configuration Example**:
```hcl
folders = {
  "root_folders" = {
    parent  = "organizations/373017380746"
    folders = ["fldr-mvt"]
  }
  "env_folders" = {
    parent  = "folders/568337859933"
    folders = ["fldr-automation", "fldr-shared", "fldr-nonprod", "fldr-prod"]
  }
}
```

### 2. Hub Networking (`Components/Internal/hub/`)
**Purpose**: Creates the central networking hub with VPCs, subnets, and connectivity.

**Key Features**:
- **Host Project**: Shared VPC host for centralized networking
- **Multiple VPCs**: Environment-specific VPCs (nonprod, dev, qa, demo, devops, prod, live, cuat)
- **Subnet Management**: Dynamic subnet creation with proper naming
- **VPC Peering**: Inter-environment connectivity
- **Organization Policies**: Subnet access control
- **HA VPN**: GCP-Azure connectivity

**Network Configuration Example**:
```hcl
network_config = {
  "nonprod/01" = {
    primary_cidr_range = "***********/28"
  }
  "sun/dev/01" = {
    primary_cidr_range = "*********/28"
    subnet_allowed_list = ["proj-dev-sun-01"]
  }
}
```

**Subnet Naming Convention**:
- Hub subnets: `snet-{env}-vm-{number}` (e.g., `snet-nonprod-vm-01`)
- Client subnets: `snet-{env}-{client}-vm-{number}` (e.g., `snet-dev-sun-vm-01`)

### 3. Projects (`Components/Internal/projects/`)
**Purpose**: Creates GCP projects with proper configuration.

**Features**:
- Project creation with billing account association
- API enablement
- Service account creation
- Shared VPC attachment

### 4. Virtual Machines (`Components/Internal/vm/`)
**Purpose**: VM deployment with standardized configurations.

**VM Configuration Example**:
```hcl
vms = {
  "vm-nonprod-jumphost-01" = {
    zone              = "europe-west2-a"
    machine_type      = "e2-custom-2-4096"
    resource_policies = ["daily-instance-schedule-policy"]
    boot_disk_size    = 50
    boot_disk_type    = "pd-balanced"
    boot_disk_image   = "windows-server-2025-dc-v20250710"
    tags              = ["iap", "jumphost", "egress-internet"]
    labels = {
      environment = "nonprod"
      application = "jumphost"
    }
  }
}
```

### 5. IAM Management (`Components/Internal/iam/`)
**Purpose**: Centralized IAM role and permission management.

**Supports**:
- Project-level IAM
- Folder-level IAM
- Multiple roles per member
- Service account management

### 6. Organization Policies (`Components/Internal/org_policies/`)
**Purpose**: Enforces organizational security and compliance policies.

**Available Policies**:
- Domain restricted sharing
- VM external IP restrictions
- Bucket policy enforcement
- Protocol forwarding restrictions

## Reusable Modules

### Networking Module (`modules/networking/`)
**Purpose**: Comprehensive networking setup including VPCs, subnets, NAT, and peering.

**Key Components**:
- VPC creation with environment-specific naming
- Subnet creation with CIDR management
- Private Service Access (PSA) configuration
- VPC peering setup
- Subnet IAM for shared VPC

### VM Module (`modules/vm/`)
**Purpose**: Standardized VM deployment with security best practices.

**Features**:
- Custom machine types
- Disk management (boot + additional disks)
- Network configuration
- Service account attachment
- Resource policy association
- Metadata and startup scripts

### Project Module (`modules/project/`)
**Purpose**: Project creation with standard configurations.

**Includes**:
- Project creation with proper naming
- Billing account association
- API enablement
- Host/Service project configuration
- Label management

## Environment Management

### Environment Structure
- **NonProd**: `nonprod`, `dev`, `qa`, `demo`, `devops`
- **Prod**: `prod`, `live`, `cuat`

### Variable Files Location
- Internal configs: `Environments/Internal/{component}/{env}.tfvars`
- Customer configs: `Environments/Customer/{component}/{env}.tfvars`

## Security and Best Practices

### Network Security
1. **Shared VPC Architecture**: Centralized network management
2. **Firewall Rules**: Environment-specific and common rules
3. **Private IP Access**: VMs use private IPs by default
4. **IAP Access**: Identity-Aware Proxy for secure access

### Organization Policies
1. **Subnet Access Control**: Restricts which projects can use specific subnets
2. **Domain Restrictions**: Controls external sharing
3. **VM External IPs**: Restricts public IP assignment

### Service Accounts
- **Default Pattern**: `id-{env}-vm-default@mvtech-{project}.iam.gserviceaccount.com`
- **Purpose-Specific**: `id-{env}-{purpose}@mvtech-{project}.iam.gserviceaccount.com`

## CI/CD Pipeline Integration

### Pipeline Structure
- **Trigger**: Branch-based deployment (develop branch)
- **Environments**: Separate pipelines for NonProd and Prod
- **State Management**: GCS backend with environment-specific prefixes
- **Security**: Workload Identity Federation for authentication

### Pipeline Features
- Plan-only for Pull Requests
- Destroy capability with parameters
- Force apply option
- Environment-specific variable injection

## Getting Started

### Prerequisites
1. GCP Organization access
2. Terraform >= 1.3
3. Appropriate IAM permissions
4. Azure DevOps access (for pipelines)

### Basic Deployment Steps
1. **Configure Variables**: Update `.tfvars` files for your environment
2. **Initialize Terraform**: `terraform init`
3. **Plan Deployment**: `terraform plan -var-file="path/to/env.tfvars"`
4. **Apply Changes**: `terraform apply -var-file="path/to/env.tfvars"`

### Adding New Resources

#### Adding a New Subnet
1. Update `network_config` in hub tfvars:
```hcl
"client/env/01" = {
  primary_cidr_range = "10.x.x.x/28"
  subnet_allowed_list = ["proj-env-client-01"]
}
```

#### Adding a New VM
1. Update VM configuration in environment tfvars:
```hcl
vms = {
  "vm-name" = {
    zone = "europe-west2-a"
    machine_type = "e2-medium"
    # ... other configurations
  }
}
```

#### Adding a New Project
1. Create project configuration
2. Update environment variables
3. Configure shared VPC attachment if needed

## Important Considerations

### Naming Conventions
- **Consistency**: Follow established patterns
- **Environment Prefixes**: Always include environment in names
- **Client Identification**: Include client name for multi-tenant resources

### Network Planning
- **CIDR Allocation**: Plan IP ranges carefully to avoid conflicts
- **Subnet Sizing**: Consider future growth
- **Connectivity**: Plan VPC peering and VPN requirements

### Security
- **Least Privilege**: Grant minimum required permissions
- **Network Segmentation**: Use separate subnets for different tiers
- **Monitoring**: Enable logging and monitoring for all resources

### Cost Management
- **Resource Policies**: Use scheduling policies for non-production VMs
- **Right-sizing**: Choose appropriate machine types
- **Cleanup**: Regularly review and remove unused resources

## Support and Maintenance

### Monitoring
- **State Files**: Stored in GCS with versioning
- **Logs**: Available through Azure DevOps pipelines
- **Drift Detection**: Regular terraform plan runs

### Troubleshooting
1. **State Issues**: Check GCS backend connectivity
2. **Permission Errors**: Verify IAM roles and Workload Identity
3. **Network Issues**: Check firewall rules and VPC configuration
4. **Resource Conflicts**: Review naming conventions and existing resources

### Updates and Changes
- **Module Updates**: Test in non-production first
- **Variable Changes**: Update all relevant environment files
- **Documentation**: Keep this guide updated with changes

## Advanced Features

### Machine Images (`modules/machine_image/`)
**Purpose**: Create and manage custom VM images for standardized deployments.

**Use Cases**:
- Pre-configured application images
- Security-hardened base images
- Database templates

**Example Configuration**:
```hcl
vms_from_machine_image = {
  "db-server-01" = {
    machine_image_name = "oracle-image"
    machine_image_project_id = "mvtech-proj-dev-sun-01"
    labels = {
      application = "database"
      install-ops-agent = "yes"
    }
    deletion_protection = false
    metadata = {
      enable-osconfig = "TRUE"
    }
  }
}
```

### Resource Policies (`modules/resource_policy/`)
**Purpose**: Automated resource management and scheduling.

**Features**:
- VM start/stop scheduling
- Backup policies
- Ops Agent installation policies

**Example**:
```hcl
resource_policies = ["daily-instance-schedule-policy"]
```

### HA VPN (`modules/ha_vpn_gcp_azure/`)
**Purpose**: High-availability VPN connectivity between GCP and Azure.

**Configuration Example**:
```hcl
vpn_gcp_azure_config = {
  "nonprod-hub" = {
    gcp_vpc_name = "hub-nonprod-01"
    gcp_router_interface1_ip_range = "************/30"
    gcp_router_peer_ip_address1 = "************"
    azure_vpn_gateway_public_ip_1 = "*************"
    shared_key = "your-shared-key"
  }
}
```

### Cloud Identity Groups (`modules/groups/`)
**Purpose**: Manage Google Cloud Identity groups for access control.

**Features**:
- Group creation and management
- Member management
- Integration with IAM policies

## Firewall Management

### Firewall Rules Structure
The infrastructure supports both common and environment-specific firewall rules:

**Common Rules**: Applied across all environments
**Customer-Specific Rules**: Environment or client-specific rules

**Rule Categories**:
1. **Internal Communication**: Allow traffic within VPC/subnets
2. **External Access**: Controlled access from office/VPN
3. **Service Communication**: Inter-service connectivity
4. **Management Access**: IAP and administrative access

**Example Firewall Rule**:
```hcl
{
  name = "fw-dev-ig-allow-internal"
  network = "vpc-dev-01"
  direction = "INGRESS"
  description = "Allow internal communication"
  source_ranges = ["*********/28"]
  destination_ranges = ["*********/28"]
  priority = 59000
  allow = [{
    protocol = "all"
    ports = []
  }]
}
```

## Storage and Backup

### Cloud Storage (`modules/buckets/`)
**Purpose**: Standardized bucket creation with security policies.

**Features**:
- Lifecycle management
- IAM integration
- Versioning and retention policies
- Encryption configuration

### Log Sinks (`modules/logsink/`)
**Purpose**: Centralized logging and audit trail management.

**Capabilities**:
- Export logs to Cloud Storage
- BigQuery integration
- Pub/Sub streaming
- Custom log filters

## Monitoring and Observability

### Ops Agent Integration
- **Automatic Installation**: Via resource policies
- **Metrics Collection**: System and application metrics
- **Log Aggregation**: Centralized log management
- **Alerting**: Integration with Cloud Monitoring

### Resource Labeling Strategy
**Standard Labels**:
- `environment`: Environment identifier
- `application`: Application or service name
- `client`: Client identifier (for multi-tenant)
- `cost-center`: For billing allocation
- `install-ops-agent`: Ops agent installation flag

## Multi-Tenant Architecture

### Client Isolation
1. **Project Separation**: Each client gets dedicated projects
2. **Network Segmentation**: Client-specific subnets
3. **IAM Boundaries**: Role-based access control
4. **Resource Tagging**: Clear ownership identification

### Shared Services
- **Hub Networking**: Centralized connectivity
- **Monitoring**: Shared observability stack
- **Security**: Common security policies
- **Backup**: Centralized backup services

## Disaster Recovery and Business Continuity

### Backup Strategy
1. **VM Snapshots**: Automated via resource policies
2. **Database Backups**: Application-specific backup procedures
3. **Configuration Backup**: Terraform state and configurations
4. **Cross-Region Replication**: For critical data

### Recovery Procedures
1. **Infrastructure Recovery**: Terraform-based reconstruction
2. **Data Recovery**: From snapshots and backups
3. **Network Recovery**: VPN and connectivity restoration
4. **Application Recovery**: Service-specific procedures

## Cost Optimization

### Resource Scheduling
- **Development VMs**: Auto-stop during non-business hours
- **Test Environments**: Weekend shutdown policies
- **Seasonal Workloads**: Scale down during low-usage periods

### Right-Sizing Recommendations
1. **Monitor Usage**: Use Cloud Monitoring metrics
2. **Adjust Machine Types**: Based on actual utilization
3. **Disk Optimization**: Use appropriate disk types
4. **Network Optimization**: Minimize cross-region traffic

### Budget Controls
- **Project-Level Budgets**: Per-client cost tracking
- **Alert Thresholds**: Proactive cost monitoring
- **Resource Quotas**: Prevent runaway costs
- **Regular Reviews**: Monthly cost optimization sessions

## Compliance and Governance

### Security Compliance
1. **Organization Policies**: Enforce security standards
2. **IAM Best Practices**: Least privilege access
3. **Network Security**: Private-by-default architecture
4. **Audit Logging**: Comprehensive audit trails

### Data Governance
1. **Data Classification**: Sensitive data identification
2. **Encryption**: At-rest and in-transit encryption
3. **Access Controls**: Role-based data access
4. **Retention Policies**: Automated data lifecycle

## Troubleshooting Guide

### Common Issues and Solutions

#### 1. Terraform State Lock Issues
**Problem**: State file locked during concurrent operations
**Solution**:
```bash
terraform force-unlock <lock-id>
```

#### 2. Subnet Access Denied
**Problem**: VM cannot access shared VPC subnet
**Solution**: Check `subnet_allowed_list` in network configuration

#### 3. API Not Enabled
**Problem**: GCP API not enabled for required services
**Solution**: Add API to `enable_apis` list in project configuration

#### 4. IAM Permission Errors
**Problem**: Insufficient permissions for resource creation
**Solution**: Verify service account has required roles

#### 5. Network Connectivity Issues
**Problem**: VMs cannot communicate across environments
**Solution**: Check VPC peering configuration and firewall rules

### Debugging Commands
```bash
# Check Terraform state
terraform show

# Validate configuration
terraform validate

# Check resource dependencies
terraform graph

# Import existing resources
terraform import <resource_type>.<name> <resource_id>
```

## Migration and Upgrades

### Terraform Version Upgrades
1. **Test in Non-Production**: Always test upgrades first
2. **State Backup**: Create state backups before upgrades
3. **Provider Updates**: Update provider versions carefully
4. **Module Updates**: Test module updates independently

### Infrastructure Migration
1. **Blue-Green Deployments**: For zero-downtime migrations
2. **Gradual Migration**: Move services incrementally
3. **Rollback Plans**: Always have rollback procedures
4. **Data Migration**: Plan data transfer strategies

---

*This comprehensive guide covers all aspects of the GCP Terraform infrastructure. For specific technical details, refer to the individual module documentation and configuration examples in the repository. Regular updates to this documentation ensure it remains current with infrastructure changes.*
