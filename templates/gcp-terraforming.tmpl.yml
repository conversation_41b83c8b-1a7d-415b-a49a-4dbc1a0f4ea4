# This is a reusable Azure DevOps pipeline template for deploying Terraform to GCP.
# It follows a Plan -> Manual Approval -> Apply workflow.

parameters:
  - name: name
    type: string
    default: 'Terraform_GCP'
  - name: environment # The Azure DevOps Environment to target for approvals
    type: string
    default: 'dev'
  - name: workingDir # The directory where your .tf files are located
    type: string
  - name: variableDirectory # The path to your .tfvars file, without the extension
    type: string
  - name: azureServiceConnectionForWIF # The name of the Azure Service Connection federated with GCP WIF.
    type: string  
  - name: backendGcsBucket # The GCS bucket used for storing Terraform state
    type: string
  - name: backendGcsPrefix # The path/prefix for the state file within the GCS bucket
    type: string
  - name: tfVersion
    type: string
    default: '1.12.1'
  - name: tfPlanName # A unique name for the Terraform plan artifact
    type: string
  - name: tfDestroy
    type: boolean
    default: false
  - name: forceTerraform
    type: boolean
    default: false
  - name: condition
    type: string
    default: 'succeeded()'
  - name: planOnly
    type: boolean
    default: false

stages:
  - stage: ${{ parameters.name }}
    condition: ${{ parameters.condition }}
    jobs:
    - deployment: TerraformPlan
      displayName: 'Terraform Plan'
      environment: ${{ parameters.environment }}
      pool:
        vmImage: 'ubuntu-latest'
      workspace:
        clean: all
      strategy:
        runOnce:
          deploy:
            steps:
            - checkout: self
              displayName: 'Checkout code'

            - bash: |
                echo "💡 Branch: $BUILD_SOURCEBRANCH"
                echo "💡 Terraform version: ${{ parameters.tfVersion }}"
                echo "💡 Destroy terraform: ${{ parameters.tfDestroy }}"
                echo "💡 Working Directory: ${{ parameters.workingDir }}"
                echo "💡 Variable File Path: ${{ parameters.variableDirectory }}.tfvars"
                echo "💡 Azure Service Connection for WIF: ${{ parameters.azureServiceConnectionForWIF }}"
                echo "💡 GCS Backend Bucket: ${{ parameters.backendGcsBucket }}"
                echo "💡 GCS Backend Prefix: ${{ parameters.backendGcsPrefix }}"
              displayName: 'Deployment Info'

            - script: |
                if [ ${{ parameters.tfDestroy }} == True ]; then
                  echo "Setting tfDestroyCommand to '-destroy'"
                  echo "##vso[task.setvariable variable=tfDestroyCommand]-destroy"
                else
                  echo "tfDestroy is false. Skipping destroy command setup."
                  echo "##vso[task.setvariable variable=tfDestroyCommand]"
                fi
              displayName: 'Set tfDestroyCommand Variable'

            - task: AzureCLI@2
              displayName: 'Authenticate to GCP via Workload Identity Federation'
              inputs:
                azureSubscription: ${{ parameters.azureServiceConnectionForWIF }}
                addSpnToEnvironment: true
                scriptType: 'bash'
                scriptLocation: 'inlineScript'
                inlineScript: |
                  echo "Setting up GCP credentials for WIF..."
                  
                  # Use the idToken environment variable
                  if [ ! -z "$idToken" ]; then
                    echo "Using idToken from service connection"
                    TOKEN_VALUE="$idToken"
                  else
                    echo "##[error]idToken not available. Ensure service connection is configured for Workload Identity Federation"
                    exit 1
                  fi
                  
                  # Write the token
                  echo "Writing OIDC token to file..."
                  echo "$TOKEN_VALUE" > $(Pipeline.Workspace)/azure_token.jwt
                  
                  # Verify token was written
                  if [ ! -s "$(Pipeline.Workspace)/azure_token.jwt" ]; then
                    echo "##[error]Failed to write token to file"
                    exit 1
                  fi
                  
                  echo "Token written successfully"
                  
                  # Create GCP credentials file
                  cat << EOF > $(Pipeline.Workspace)/gcp_credentials.json
                  {
                    "type": "external_account",
                    "audience": "//iam.googleapis.com/projects/$(GCP_PROJECT_NUMBER)/locations/global/workloadIdentityPools/$(GCP_WIP_POOL)/providers/$(GCP_WIP_PROVIDER)",
                    "subject_token_type": "urn:ietf:params:oauth:token-type:jwt",
                    "token_url": "https://sts.googleapis.com/v1/token",
                    "credential_source": {
                      "file": "$(Pipeline.Workspace)/azure_token.jwt"
                    },
                    "service_account_impersonation_url": "https://iamcredentials.googleapis.com/v1/projects/-/serviceAccounts/$(GCP_SERVICE_ACCOUNT):generateAccessToken"
                  }
                  EOF
                  
                  echo "GCP credentials file created"
              env:
                SYSTEM_ACCESSTOKEN: $(System.AccessToken)

            - bash: |
                echo "Setting GOOGLE_APPLICATION_CREDENTIALS environment variable"
                echo "##vso[task.setvariable variable=GOOGLE_APPLICATION_CREDENTIALS]$(Pipeline.Workspace)/gcp_credentials.json"
              displayName: 'Set GCP Credentials Env Var'

            - task: TerraformInstaller@1
              displayName: 'Install Terraform ${{ parameters.tfVersion }}'
              inputs:
                terraformVersion: ${{ parameters.tfVersion }}

            - task: Bash@3
              displayName: 'Terraform Init'
              inputs:
                targetType: 'inline'
                workingDirectory: ${{ parameters.workingDir }}
                script: |
                  terraform init \
                    -backend-config="bucket=${{ parameters.backendGcsBucket }}" \
                    -backend-config="prefix=${{ parameters.backendGcsPrefix }}"

            - task: Bash@3
              displayName: 'Terraform Plan'
              inputs:
                targetType: 'inline'
                workingDirectory: ${{ parameters.workingDir }}
                script: |
                  set -e
                  set -o pipefail

                  terraform plan \
                    -var-file="${{ parameters.variableDirectory }}.tfvars" \
                    -out=tfplan $(tfDestroyCommand)
                  
                  # Check if plan has changes
                  if terraform show tfplan | grep -q "No changes"; then
                    echo "##vso[task.setvariable variable=TERRAFORM_PLAN_HAS_CHANGES;isOutput=true]false"
                  else
                    echo "##vso[task.setvariable variable=TERRAFORM_PLAN_HAS_CHANGES;isOutput=true]true"
                  fi
              name: TerraformPlanTask

            - task: PublishPipelineArtifact@1
              displayName: 'Publish Terraform Plan'
              inputs:
                targetPath: '${{ parameters.workingDir }}/tfplan'
                artifact: ${{ parameters.tfPlanName }}

    - deployment: TerraformApprove
      displayName: 'Terraform Approval'
      environment: ${{ parameters.environment }}
      dependsOn: TerraformPlan
      condition: and(not(${{ parameters.planOnly }}), succeeded('TerraformPlan'), eq(dependencies.TerraformPlan.outputs['TerraformPlan.TerraformPlanTask.TERRAFORM_PLAN_HAS_CHANGES'], 'true'), or(eq(variables['Build.SourceBranch'], 'refs/heads/develop'), eq('${{ parameters.forceTerraform }}', 'true')))
      pool: server
      strategy:
        runOnce:
          deploy:
            steps:
              - task: ManualValidation@0
                timeoutInMinutes: 1440
                inputs:
                  instructions: |
                    Terraform plan for **${{ parameters.environment }}** requires approval.
                    Review the plan output before approving.
                  onTimeout: 'reject'

    - deployment: TerraformApply
      displayName: 'Terraform Apply'
      environment: ${{ parameters.environment }}
      dependsOn:
        - TerraformPlan
        - TerraformApprove
      condition: and(not(${{ parameters.planOnly }}), succeeded('TerraformApprove'))
      pool:
        vmImage: 'ubuntu-latest'
      strategy:
        runOnce:
          deploy:
            steps:
            - checkout: self
              displayName: 'Checkout code'

            - task: DownloadPipelineArtifact@2
              displayName: 'Download Plan'
              inputs:
                artifact: ${{ parameters.tfPlanName }}
                path: $(Pipeline.Workspace)/${{ parameters.tfPlanName }}

            - task: AzureCLI@2
              displayName: 'Authenticate to GCP via Workload Identity Federation'
              inputs:
                azureSubscription: ${{ parameters.azureServiceConnectionForWIF }}
                addSpnToEnvironment: true
                scriptType: 'bash'
                scriptLocation: 'inlineScript'
                inlineScript: |
                  echo "Setting up GCP credentials for WIF..."
                  
                  # Use the idToken environment variable
                  if [ ! -z "$idToken" ]; then
                    echo "Using idToken from service connection"
                    TOKEN_VALUE="$idToken"
                  else
                    echo "##[error]idToken not available. Ensure service connection is configured for Workload Identity Federation"
                    exit 1
                  fi
                  
                  # Write the token
                  echo "Writing OIDC token to file..."
                  echo "$TOKEN_VALUE" > $(Pipeline.Workspace)/azure_token.jwt
                  
                  # Verify token was written
                  if [ ! -s "$(Pipeline.Workspace)/azure_token.jwt" ]; then
                    echo "##[error]Failed to write token to file"
                    exit 1
                  fi
                  
                  echo "Token written successfully"
                  
                  # Create GCP credentials file
                  cat << EOF > $(Pipeline.Workspace)/gcp_credentials.json
                  {
                    "type": "external_account",
                    "audience": "//iam.googleapis.com/projects/$(GCP_PROJECT_NUMBER)/locations/global/workloadIdentityPools/$(GCP_WIP_POOL)/providers/$(GCP_WIP_PROVIDER)",
                    "subject_token_type": "urn:ietf:params:oauth:token-type:jwt",
                    "token_url": "https://sts.googleapis.com/v1/token",
                    "credential_source": {
                      "file": "$(Pipeline.Workspace)/azure_token.jwt"
                    },
                    "service_account_impersonation_url": "https://iamcredentials.googleapis.com/v1/projects/-/serviceAccounts/$(GCP_SERVICE_ACCOUNT):generateAccessToken"
                  }
                  EOF
                  
                  echo "GCP credentials file created"
              env:
                SYSTEM_ACCESSTOKEN: $(System.AccessToken)

            - bash: |
                echo "Setting GOOGLE_APPLICATION_CREDENTIALS environment variable"
                echo "##vso[task.setvariable variable=GOOGLE_APPLICATION_CREDENTIALS]$(Pipeline.Workspace)/gcp_credentials.json"
              displayName: 'Set GCP Credentials Env Var'

            - task: TerraformInstaller@1
              displayName: 'Install Terraform ${{ parameters.tfVersion }}'
              inputs:
                terraformVersion: ${{ parameters.tfVersion }}

            - task: Bash@3
              displayName: 'Terraform Init'
              inputs:
                targetType: 'inline'
                workingDirectory: ${{ parameters.workingDir }}
                script: |
                  terraform init \
                    -backend-config="bucket=${{ parameters.backendGcsBucket }}" \
                    -backend-config="prefix=${{ parameters.backendGcsPrefix }}"

            - task: Bash@3
              displayName: 'Copy Plan File'
              inputs:
                targetType: 'inline'
                script: |
                  cp $(Pipeline.Workspace)/${{ parameters.tfPlanName }}/tfplan ${{ parameters.workingDir }}/tfplan

            - task: Bash@3
              displayName: 'Terraform Apply'
              inputs:
                targetType: 'inline'
                workingDirectory: ${{ parameters.workingDir }}
                script: |
                  terraform apply tfplan
