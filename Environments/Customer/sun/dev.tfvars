
project_name            = "proj-dev-sun-01"
env                     = "dev"
enable_apis             = []
create_ops_agent_policy = true

# custom_service_accounts = [
#   {
#     sa_purpose = "cloudrun-default"
#   }
# ]
##### VM Configuration #####

vms = {
  "vm-dev-sun-db-01" = {
    zone            = "europe-west2-a"
    machine_type    = "c4-standard-4"
    boot_disk_size  = 30
    boot_disk_type  = "hyperdisk-balanced"
    nic_type        = "GVNIC"
    boot_disk_image = "rhel-8-v20250709"
    tags            = ["iap", "egress-internet"]
    labels = {
      environment       = "dev"
      application       = "database"
      customer          = "sun"
      install-ops-agent = "yes" // to install ops agent on this VM
    }
    attached_disks = [
      {
        name = "u10"
        size = 100
        type = "hyperdisk-balanced"
        mode = "READ_WRITE"
      },
      {
        name = "backup"
        size = 70
        type = "hyperdisk-balanced"
        mode = "READ_WRITE"
      }
    ]
    metadata = {
      enable-osconfig = "TRUE"
    }
  }

  "vm-dev-sun-forms-01" = {
    zone            = "europe-west2-a"
    machine_type    = "c4-standard-2"
    boot_disk_size  = 30
    boot_disk_type  = "hyperdisk-balanced"
    nic_type        = "GVNIC"
    boot_disk_image = "rhel-8-v20250709"
    tags            = ["iap", "egress-internet"]
    labels = {
      environment       = "dev"
      application       = "forms"
      customer          = "sun"
      install-ops-agent = "yes" // to install ops agent on this VM
    }
    attached_disks = [
      {
        name = "u10"
        size = 50
        type = "hyperdisk-balanced"
        mode = "READ_WRITE"
      },
      {
        name = "backup"
        size = 50
        type = "hyperdisk-balanced"
        mode = "READ_WRITE"
      }
    ]
    metadata = {
      enable-osconfig = "TRUE"
    }
  }
}

##### Project IAM #####

project_iam_config = {
  "id-dev-vm-default" = {
    project = "mvtech-proj-dev-sun-01"
    member  = "serviceAccount:<EMAIL>"
    roles = [
      "roles/logging.logWriter",
      "roles/monitoring.metricWriter"
    ]
  }
}

##### Bucket Configurations #####

bucket_configs = {
  "storage-dev-sun-db-backup" = {
    location                    = "europe-west2"
    storage_class               = "STANDARD"
    public_access_prevention    = "enforced"
    uniform_bucket_level_access = true
    iam_members = [
      {
        role    = "roles/storage.objectAdmin"
        members = ["serviceAccount:<EMAIL>"]
      }
    ]
  }
}

##### Machine Images #####

create_machine_image = false
# machine_images = {
#   "vm-dev-sun-db-test-mi-01" = {
#     source_instance = "vm-dev-sun-db-test"
#     zone = "europe-west2-b"
#   }
# }

##### VMs from Machine Image #####

# vms_from_machine_image = {
#   "vm-dev-sun-db-02" = {
#     machine_image_name = "oracle-image"
#     #machine_image_project_id = "mvtech-proj-dev-sun-01" //when image is in different project
#     #service_account_email = "" //when different sa needs to be attached to the vm then the vm-default one
#     labels = {
#       environment       = "dev"
#       application       = "database"
#       customer          = "sun"
#       install-ops-agent = "yes" // to install ops agent on this VM
#     }
#     deletion_protection = false
#     metadata = {
#       enable-osconfig = "TRUE"
#     }
#     attached_disks = [
#       {
#         name = "u10"
#         size = 50
#         type = "hyperdisk-balanced"
#         mode = "READ_WRITE"
#       },
#       {
#         name = "backup"
#         size = 50
#         type = "hyperdisk-balanced"
#         mode = "READ_WRITE"
#       }
#     ]
    
#   }
# }