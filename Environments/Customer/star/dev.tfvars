project_name = "proj-dev-star-01"
env          = "dev"
enable_apis  = []
create_ops_agent_policy = true

##### Machine Images #####
create_machine_image = false

##### VM Configuration #####
vms = {
  "vm-dev-star-forms-01" = {
    zone            = "europe-west2-a"
    machine_type    = "c4-standard-2"
    boot_disk_size  = 100
    boot_disk_type  = "hyperdisk-balanced"
    nic_type        = "GVNIC"
    boot_disk_image = "rhel-8-v20250709"
    tags            = ["iap", "egress-internet"]
    labels = {
      environment       = "dev"
      application       = "forms"
      customer          = "star"
      install-ops-agent = "yes" // to install ops agent on this VM
    }

    attached_disks = []
    metadata = {
      enable-osconfig = "TRUE"
    }
  }
}

#### VMs from Machine Image #####
vms_from_machine_image = {
  "vm-dev-star-db-01" = {
    machine_image_name = "oracle-image-for-pro12-db"
    machine_image_project_id = "mvtech-proj-nonprod-shared-01" //when image is in different project
    #service_account_email = "" //when different sa needs to be attached to the vm then the vm-default one
    labels = {
      environment       = "dev"
      application       = "database"
      customer          = "star"
      install-ops-agent = "yes" // to install ops agent on this VM
    }
    deletion_protection = false
    metadata = {
      enable-osconfig = "TRUE"
    }
    attached_disks = [
      {
        name = "u01"
        size = 100
        type = "hyperdisk-balanced"
        mode = "READ_WRITE"
      },
      {
        name = "backup"
        size = 80
        type = "hyperdisk-balanced"
        mode = "READ_WRITE"
      }
    ]

  }
}

##### Project IAM #####
project_iam_config = {
  "id-dev-vm-default" = {
    project = "mvtech-proj-dev-star-01"
    member = "serviceAccount:<EMAIL>"
    roles = [
      "roles/logging.logWriter",
      "roles/monitoring.metricWriter"
    ]
  }
  }

  ##### Bucket Configurations #####

bucket_configs = {
  "storage-dev-star-db-backup" = {
    location                    = "europe-west2"
    storage_class               = "STANDARD"
    public_access_prevention    = "enforced"
    uniform_bucket_level_access = true
    iam_members = [
      {
        role    = "roles/storage.objectAdmin"
        members = ["serviceAccount:<EMAIL>"]
      }
    ]
  }
}



