project_name = "proj-dev-dummy-01"
env          = "dev"
enable_apis  = []
create_ops_agent_policy = true

# vms = {}


# #### VMs from Machine Image #####

# vms_from_machine_image = {
#   "vm-dev-dummy-db-01" = {
#     machine_image_name = "oracle-image-for-pro12-db"
#     machine_image_project_id = "mvtech-proj-nonprod-shared-01" //when image is in different project
#     #service_account_email = "" //when different sa needs to be attached to the vm then the vm-default one
#     labels = {
#       environment       = "dev"
#       application       = "database"
#       customer          = "dummy"
#       install-ops-agent = "yes" // to install ops agent on this VM
#     }
#     deletion_protection = false
#     metadata = {
#       enable-osconfig = "TRUE"
#     }
#     attached_disks = [
#       {
#         name = "u01"
#         size = 30
#         type = "hyperdisk-balanced"
#         mode = "READ_WRITE"
#       },
#       {
#         name = "backup"
#         size = 30
#         type = "hyperdisk-balanced"
#         mode = "READ_WRITE"
#       }
#     ]

#   }
# }
