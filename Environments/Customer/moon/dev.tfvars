project_name = "proj-dev-moon-01"
env          = "dev"
enable_apis  = []
create_ops_agent_policy = true

##### VM Configuration #####

vms = {
#   "vm-dev-moon-db-01" = {
#     zone            = "europe-west2-a"
#     machine_type    = "c4-standard-4"
#     boot_disk_size  = 30
#     boot_disk_type  = "hyperdisk-balanced"
#     nic_type        = "GVNIC"
#     boot_disk_image = "rhel-8-v20250709"
#     tags            = ["iap", "egress-internet"]
#     labels = {
#       environment       = "dev"
#       application       = "database"
#       customer          = "moon"
#       install-ops-agent = "yes" // to install ops agent on this VM
#     }
#     attached_disks = [
#       {
#         name = "u01"
#         size = 100
#         type = "hyperdisk-balanced"
#         mode = "READ_WRITE"
#       },
#       {
#         name = "backup"
#         size = 70
#         type = "hyperdisk-balanced"
#         mode = "READ_WRITE"
#       }
#     ]
#     metadata = {
#       enable-osconfig = "TRUE"
#     }
#   }

  "vm-dev-moon-forms-01" = {
    zone            = "europe-west2-a"
    machine_type    = "c4-standard-2"
    boot_disk_size  = 100
    boot_disk_type  = "hyperdisk-balanced"
    nic_type        = "GVNIC"
    boot_disk_image = "rhel-8-v20250709"
    tags            = ["iap", "egress-internet"]
    labels = {
      environment       = "dev"
      application       = "forms"
      customer          = "moon"
      install-ops-agent = "yes" // to install ops agent on this VM
    }

    attached_disks = []
    metadata = {
      enable-osconfig = "TRUE"
    }
  }
}

##### Machine Images #####

create_machine_image = false

#### VMs from Machine Image #####

vms_from_machine_image = {
  "vm-dev-moon-db-01" = {
    machine_image_name = "oracle-image"
    machine_image_project_id = "mvtech-proj-dev-sun-01" //when image is in different project
    #service_account_email = "" //when different sa needs to be attached to the vm then the vm-default one
    labels = {
      environment       = "dev"
      application       = "database"
      customer          = "moon"
      install-ops-agent = "yes" // to install ops agent on this VM
    }
    deletion_protection = false
    metadata = {
      enable-osconfig = "TRUE"
    }
    attached_disks = [
      {
        name = "u01"
        size = 100
        type = "hyperdisk-balanced"
        mode = "READ_WRITE"
      },
      {
        name = "backup"
        size = 80
        type = "hyperdisk-balanced"
        mode = "READ_WRITE"
      }
    ]

  }
}


##### Project IAM #####
project_iam_config = {
  "id-dev-vm-default" = {
    project = "mvtech-proj-dev-moon-01"
    member = "serviceAccount:<EMAIL>"
    roles = [
      "roles/logging.logWriter",
      "roles/monitoring.metricWriter"
    ]
  }
  }

  ##### Bucket Configurations #####

bucket_configs = {
  "storage-dev-moon-db-backup" = {
    location                    = "europe-west2"
    storage_class               = "STANDARD"
    public_access_prevention    = "enforced"
    uniform_bucket_level_access = true
    iam_members = [
      {
        role    = "roles/storage.objectAdmin"
        members = ["serviceAccount:<EMAIL>"]
      }
    ]
  }
}