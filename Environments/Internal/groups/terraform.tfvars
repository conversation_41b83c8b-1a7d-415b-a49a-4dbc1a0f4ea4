organization_domain = "mutualvision.co.uk"
#<EMAIL>
groups = {
  "gcp-org-admins" = {
    members = [
      "user:<EMAIL>",
      "user:<EMAIL>",
      "user:<EMAIL>"
    ]
  }
  "gcp-org-viewers" = {
    members = [
      "user:<EMAIL>",
      "user:<EMAIL>"
    ]
  }
  "gcp-network-admins" = {
    members = [
      "user:<EMAIL>",
      "user:<EMAIL>",
      "user:<EMAIL>",
      "user:<EMAIL>"
    ]
  }
  "gcp-security-admins" = {
    members = [
      "user:<EMAIL>",
      "user:<EMAIL>",
      "user:<EMAIL>",
      "user:<EMAIL>"
    ]
  }
  "gcp-logging-admins" = {
    members = [
      "user:<EMAIL>",
      "user:<EMAIL>",
      "user:<EMAIL>",
      "user:<EMAIL>"
    ]
  }
  "gcp-logging-viewers" = {
    members = [
      "user:<EMAIL>",
      "user:<EMAIL>",
      "user:<EMAIL>",
      "user:<EMAIL>"
    ]
  }
  "gcp-monitoring-admins" = {
    members = [
      "user:<EMAIL>",
      "user:<EMAIL>",
      "user:<EMAIL>",
      "user:<EMAIL>"
    ]
  }

  ###### Temp Searce Groups #####

  "searce-architects" = {
    allow_external_members = true
    members = [
      "user:<EMAIL>",
      "user:<EMAIL>"
    ]
  }
  "searce-engineers" = {
    allow_external_members = true
    members = [
      "user:<EMAIL>",
      "user:<EMAIL>"
    ]
  }
  "searce-dba" = {
    allow_external_members = true
    members = [
      "user:<EMAIL>",
      "user:<EMAIL>"
    ]
  }

}

