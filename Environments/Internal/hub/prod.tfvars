project_name = "proj-prod-host-01"
enable_apis  = []
project_labels = {
  environment = "prod"
}

environment  = ["prod", "live", "cuat"]
network_config = {
  "prod/01" = {
    primary_cidr_range = "*********/28"
  }
}

vpc_peering = {
  "prod-live" = {
    network      = "live"
    peer_network = "prod"
  }
  "prod-cuat" = {
    network      = "cuat"
    peer_network = "prod"
  }
}

customer_specific_rules = [
  {
    name               = "fw-prod-eg-allow-internal"
    network            = "hub-prod-01"
    direction          = "EGRESS"
    description        = "allow EGRESS within MVT Prod Hub VPC VM subnet"
    source_ranges      = ["*********/28"]
    destination_ranges = ["*********/28"]
    priority           = 60000
    target_tags        = []
    allow = [{
      protocol = "all"
      ports    = []
    }]
    deny       = []
    log_config = null
  },
  {
    name               = "fw-prod-in-allow-internal"
    network            = "hub-prod-01"
    direction          = "INGRESS"
    description        = "allow INGRESS within MVT Prod Hub VPC VM subnet"
    source_ranges      = ["*********/28"]
    destination_ranges = ["*********/28"]
    priority           = 59000
    target_tags        = []
    allow = [{
      protocol = "all"
      ports    = []
    }]
    deny       = []
    log_config = null
  }
]