project_name = "proj-prod-host-01"
enable_apis  = []
project_labels = {
  environment = "prod"
}

environment  = ["prod", "live", "cuat"]
network_config = {
  "prod/01" = {
    primary_cidr_range = "*********/28"
    # subnet_allowed_list = ["proj-prod-host-01"]
  }
}

vpc_peering = {
  "prod-live" = {
    network      = "live"
    peer_network = "prod"
  }
  "prod-cuat" = {
    network      = "cuat"
    peer_network = "prod"
  }
}

customer_specific_rules = [
  {
    name               = "fw-prod-eg-allow-internal"
    network            = "hub-prod-01"
    direction          = "EGRESS"
    description        = "allow EGRESS within MVT Prod Hub VPC VM subnet"
    source_ranges      = ["*********/28"]
    destination_ranges = ["*********/28"]
    priority           = 60000
    target_tags        = []
    allow = [{
      protocol = "all"
      ports    = []
    }]
    deny       = []
    log_config = null
  },
  {
    name               = "fw-prod-in-allow-internal"
    network            = "hub-prod-01"
    direction          = "INGRESS"
    description        = "allow INGRESS within MVT Prod Hub VPC VM subnet"
    source_ranges      = ["*********/28"]
    destination_ranges = ["*********/28"]
    priority           = 59000
    target_tags        = []
    allow = [{
      protocol = "all"
      ports    = []
    }]
    deny       = []
    log_config = null
  },
  {
    name               = "fw-cuat-in-allow-azure-prod-hub-vpn"
    network            = "vpc-cuat-01"
    direction          = "INGRESS"
    description        = "allow INGRESS from Azure Prod Hub VPC VM subnet to CUAT VPC"
    source_ranges      = ["**********/16","**********/16","**********/16","**********/16","*********/16","*********/16"]
    destination_ranges = ["*********/16"]
    priority           = 61000
    target_tags        = []
    allow = [{
      protocol = "tcp"
      ports    = ["22","1521","4443","7001","7002","9001"]
    }]
    deny       = []
    log_config = null
  },
  {
    name               = "fw-cuat-in-allow-azure-prod-hub-dr-vpn"
    network            = "vpc-cuat-01"
    direction          = "INGRESS"
    description        = "allow INGRESS from Azure Prod Hub DR VPC VM subnet to CUAT VPC"
    source_ranges      = ["**********/16"]
    destination_ranges = ["*********/16"]
    priority           = 61000
    target_tags        = []
    allow = [{
      protocol = "tcp"
      ports    = ["22","1521","4443","7001","7002","9001"]
    }]
    deny       = []
    log_config = null
  },
  {
    name               = "fw-cuat-in-allow-azure-prod-hub-ih-vpn"
    network            = "vpc-cuat-01"
    direction          = "INGRESS"
    description        = "allow INGRESS from Azure Prod Hub IH VPC VM subnet to CUAT VPC"
    source_ranges      = ["**********/16","**********/16","**********/16"]
    destination_ranges = ["*********/16"]
    priority           = 61000
    target_tags        = []
    allow = [{
      protocol = "tcp"
      ports    = ["22","1521","4443","7001","7002","9001"]
    }]
    deny       = []
    log_config = null
  },
  {
    name               = "fw-live-in-allow-azure-prod-hub-vpn"
    network            = "vpc-live-01"
    direction          = "INGRESS"
    description        = "allow INGRESS from Azure Prod Hub VPC VM subnet to LIVE VPC"
    source_ranges      = ["**********/16","**********/16","**********/16","**********/16","*********/16","*********/16"]
    destination_ranges = ["*********/16"]
    priority           = 61000
    target_tags        = []
    allow = [{
      protocol = "tcp"
      ports    = ["22","1521","4443","7001","7002","9001"]
    }]
    deny       = []
    log_config = null
  },
  {
    name               = "fw-live-in-allow-azure-prod-hub-dr-vpn"
    network            = "vpc-live-01"
    direction          = "INGRESS"
    description        = "allow INGRESS from Azure Prod Hub DR VPC VM subnet to LIVE VPC"
    source_ranges      = ["**********/16"]
    destination_ranges = ["*********/16"]
    priority           = 61000
    target_tags        = []
    allow = [{
      protocol = "tcp"
      ports    = ["22","1521","4443","7001","7002","9001"]
    }]
    deny       = []
    log_config = null
  },
  {
    name               = "fw-live-in-allow-azure-prod-hub-ih-vpn"
    network            = "vpc-live-01"
    direction          = "INGRESS"
    description        = "allow INGRESS from Azure Prod Hub IH VPC VM subnet to LIVE VPC"
    source_ranges      = ["**********/16","**********/16","**********/16"]
    destination_ranges = ["*********/16"]
    priority           = 61000
    target_tags        = []
    allow = [{
      protocol = "tcp"
      ports    = ["22","1521","4443","7001","7002","9001"]
    }]
    deny       = []
    log_config = null
  }
]

vpn_gcp_azure_config = {
    "prod-hub" = {
    gcp_vpc_name = "hub-prod-01"
    gcp_router_interface1_ip_range = "*************/30"
    gcp_router_peer_ip_address1 = "************"
    azure_vpn_gateway_public_ip_1 = "*************"
    gcp_router_interface2_ip_range = "*************/30"
    gcp_router_peer_ip_address2 = "************"
    shared_key = "3%XNixjI#bg8321458!!)"
  }
    "prod-hub-dr" = {
    gcp_vpc_name = "hub-prod-01"
    gcp_router_interface1_ip_range = "*************/30"
    gcp_router_peer_ip_address1 = "*************"
    azure_vpn_gateway_public_ip_1 = "*************"
    gcp_router_interface2_ip_range = "*************/30"
    gcp_router_peer_ip_address2 = "*************"
    shared_key = "3%XNixjI#bg8321458!!)"
  }
    "prod-hub-ih" = {
    gcp_vpc_name = "hub-prod-01"
    gcp_router_interface1_ip_range = "*************/30"
    gcp_router_peer_ip_address1 = "*************"
    azure_vpn_gateway_public_ip_1 = "************"
    gcp_router_interface2_ip_range = "*************/30"
    gcp_router_peer_ip_address2 = "*************"
    shared_key = "3%XNixjI#bg8321458!!)"
  }
}