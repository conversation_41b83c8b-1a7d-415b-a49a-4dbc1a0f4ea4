project_name = "proj-nonprod-host-01"
enable_apis  = []
project_labels = {
  environment = "nonprod"
}

environment  = ["nonprod", "dev", "qa", "demo", "devops"]

network_config = {
  "nonprod/01" = {
    primary_cidr_range = "***********/28"
    # subnet_allowed_list = ["proj-nonprod-host-01"]
  }
  "sun/dev/01" = {
    primary_cidr_range = "*********/28"
    subnet_allowed_list = ["proj-dev-sun-01"]
    # psa_cidr = "<range>"
  }
  "moon/dev/01" = {
    primary_cidr_range = "**********/28"
    subnet_allowed_list = ["proj-dev-moon-01"]    
  }
  # "dummy/dev/01" = {
  #   primary_cidr_range = "**********/28"
  #   subnet_allowed_list = ["proj-dev-dummy-01"]    
  # }
  "star/dev/01" = {
    primary_cidr_range = "**********/28"
    subnet_allowed_list = ["proj-dev-star-01"]    
  }
}

vpc_peering = {
  "nonprod-dev" = {
    network      = "dev"
    peer_network = "nonprod"
  }
  "nonprod-qa" = {
    network      = "qa"
    peer_network = "nonprod"
  }
  "nonprod-demo" = {
    network      = "demo"
    peer_network = "nonprod"
  }
  "nonprod-devops" = {
    network      = "devops"
    peer_network = "nonprod"
  }
}

customer_specific_rules = [

  ##### hub-nonprod-01 #####
  {
    name               = "fw-nonprod-eg-allow-internal"
    network            = "hub-nonprod-01"
    direction          = "EGRESS"
    description        = "allow EGRESS within MVT NonProd Hub VPC VM subnet"
    source_ranges      = ["***********/28"]
    destination_ranges = ["***********/28"]
    priority           = 60000
    target_tags        = []
    allow = [{
      protocol = "all"
      ports    = []
    }]
    deny       = []
    log_config = null
  },
  {
    name               = "fw-nonprod-in-allow-internal"
    network            = "hub-nonprod-01"
    direction          = "INGRESS"
    description        = "allow INGRESS within MVT NonProd Hub VPC VM subnet"
    source_ranges      = ["***********/28"]
    destination_ranges = ["***********/28"]
    priority           = 59000
    target_tags        = []
    allow = [{
      protocol = "all"
      ports    = []
    }]
    deny       = []
    log_config = null
  },
  {
    name               = "fw-dev-eg-allow-internal-sun"
    network            = "vpc-dev-01"
    direction          = "EGRESS"
    description        = "allow EGRESS within sun"
    source_ranges      = ["*********/28"]
    destination_ranges = ["*********/28"]
    priority           = 60000
    target_tags        = []
    allow = [{
      protocol = "all"
      ports    = []
    }]
    deny       = []
    log_config = null
  },
  {
    name               = "fw-dev-in-allow-internal-sun"
    network            = "vpc-dev-01"
    direction          = "INGRESS"
    description        = "allow INGRESS within sun"
    source_ranges      = ["*********/28"]
    destination_ranges = ["*********/28"]
    priority           = 59000
    target_tags        = []
    allow = [{
      protocol = "all"
      ports    = []
    }]
    deny       = []
    log_config = null
  },
  {
    name               = "fw-dev-eg-allow-internal-moon"
    network            = "vpc-dev-01"
    direction          = "EGRESS"
    description        = "allow EGRESS within moon"
    source_ranges      = ["**********/28"]
    destination_ranges = ["**********/28"]
    priority           = 60000
    target_tags        = []
    allow = [{
      protocol = "all"
      ports    = []
    }]
    deny       = []
    log_config = null
  },
  {
    name               = "fw-dev-in-allow-internal-moon"
    network            = "vpc-dev-01"
    direction          = "INGRESS"
    description        = "allow INGRESS within moon"
    source_ranges      = ["**********/28"]
    destination_ranges = ["**********/28"]
    priority           = 59000
    target_tags        = []
    allow = [{
      protocol = "all"
      ports    = []
    }]
    deny       = []
    log_config = null
  },
  {
        name               = "fw-dev-in-allow-mvt-office-vpn"
        network            = "vpc-dev-01"
        direction          = "INGRESS"
        description        = "allow INGRESS from MVT Office VPN to Dev VPC"
        source_tags        = []
        source_ranges      = ["*************/24","*************/24"]
        destination_ranges = ["*********/16"]
        target_tags        = []
        priority           = 61000
        allow = [{
          protocol = "tcp"
          ports    = ["22","1521","4443","7001","7002","9001"]
        }]
        deny = []
  },
  {
    name               = "fw-dev-in-allow-azure-nonprod-hub-vpn"
    network            = "vpc-dev-01"
    direction          = "INGRESS"
    description        = "allow INGRESS from Azure NonProd Hub VPC VM subnet to Dev VPC"
    source_ranges      = ["**********/16","**********/16","**********/16","**********/16","*********/16","*********/16","*********/16","*********/16"]
    destination_ranges = ["*********/16"]
    priority           = 61000
    target_tags        = []
    allow = [{
      protocol = "tcp"
      ports    = ["22","1521","4443","7001","7002","9001"]
    }]
    deny       = []
    log_config = null
  },
  {
    name               = "fw-dev-eg-allow-internal-star"
    network            = "vpc-dev-01"
    direction          = "EGRESS"
    description        = "allow EGRESS within star"
    source_ranges      = ["**********/28"]
    destination_ranges = ["**********/28"]
    priority           = 60000
    target_tags        = []
    allow = [{
      protocol = "all"
      ports    = []
    }]
    deny       = []
    log_config = null
  },
  {
    name               = "fw-dev-in-allow-internal-star"
    network            = "vpc-dev-01"
    direction          = "INGRESS"
    description        = "allow INGRESS within star"
    source_ranges      = ["**********/28"]
    destination_ranges = ["**********/28"]
    priority           = 59000
    target_tags        = []
    allow = [{
      protocol = "all"
      ports    = []
    }]
    deny       = []
    log_config = null
  },
  {
    name               = "fw-dev-in-allow-azure-nonprod-hub-ih-vpn"
    network            = "vpc-dev-01"
    direction          = "INGRESS"
    description        = "allow INGRESS from Azure NonProd Hub IH VPC VM subnet to Dev VPC"
    source_ranges      = ["**********/16","**********/16","**********/16"]
    destination_ranges = ["*********/16"]
    priority           = 61000
    target_tags        = []
    allow = [{
      protocol = "tcp"
      ports    = ["22","1521","4443","7001","7002","9001"]
    }]
    deny       = []
    log_config = null
  },
  {
        name               = "fw-demo-in-allow-mvt-office-vpn"
        network            = "vpc-demo-01"
        direction          = "INGRESS"
        description        = "allow INGRESS from MVT Office VPN to Demo VPC"
        source_tags        = []
        source_ranges      = ["*************/24","*************/24"]
        destination_ranges = ["*********/16"]
        target_tags        = []
        priority           = 61000
        allow = [{
          protocol = "tcp"
          ports    = ["22","1521","4443","7001","7002","9001"]
        }]
        deny = []
  },
  {
    name               = "fw-demo-in-allow-azure-nonprod-hub-vpn"
    network            = "vpc-demo-01"
    direction          = "INGRESS"
    description        = "allow INGRESS from Azure NonProd Hub IH VPC VM subnet to Demo VPC"
    source_ranges      = ["**********/16","**********/16","**********/16","**********/16","*********/16","*********/16","*********/16","*********/16"]
    destination_ranges = ["*********/16"]
    priority           = 61000
    target_tags        = []
    allow = [{
      protocol = "tcp"
      ports    = ["22","1521","4443","7001","7002","9001"]
    }]
    deny       = []
    log_config = null
  },
  {
    name               = "fw-demo-in-allow-azure-nonprod-hub-ih-vpn"
    network            = "vpc-demo-01"
    direction          = "INGRESS"
    description        = "allow INGRESS from Azure NonProd Hub IH VPC VM subnet to Demo VPC"
    source_ranges      = ["**********/16","**********/16","**********/16"]
    destination_ranges = ["*********/16"]
    priority           = 61000
    target_tags        = []
    allow = [{
      protocol = "tcp"
      ports    = ["22","1521","4443","7001","7002","9001"]
    }]
    deny       = []
    log_config = null
  },
  {
        name               = "fw-devops-in-allow-mvt-office-vpn"
        network            = "vpc-devops-01"
        direction          = "INGRESS"
        description        = "allow INGRESS from MVT Office VPN to Devops VPC"
        source_tags        = []
        source_ranges      = ["*************/24","*************/24"]
        destination_ranges = ["*********/16"]
        target_tags        = []
        priority           = 61000
        allow = [{
          protocol = "tcp"
          ports    = ["22","1521","4443","7001","7002","9001"]
        }]
        deny = []
  },
  {
    name               = "fw-devops-in-allow-azure-nonprod-hub-vpn"
    network            = "vpc-devops-01"
    direction          = "INGRESS"
    description        = "allow INGRESS from Azure NonProd Hub IH VPC VM subnet to Devops VPC"
    source_ranges      = ["**********/16","**********/16","**********/16","**********/16","*********/16","*********/16","*********/16","*********/16"]
    destination_ranges = ["*********/16"]
    priority           = 61000
    target_tags        = []
    allow = [{
      protocol = "tcp"
      ports    = ["22","1521","4443","7001","7002","9001"]
    }]
    deny       = []
    log_config = null
  },
  {
    name               = "fw-devops-in-allow-azure-nonprod-hub-ih-vpn"
    network            = "vpc-devops-01"
    direction          = "INGRESS"
    description        = "allow INGRESS from Azure NonProd Hub IH VPC VM subnet to Devops VPC"
    source_ranges      = ["**********/16","**********/16","**********/16"]
    destination_ranges = ["*********/16"]
    priority           = 61000
    target_tags        = []
    allow = [{
      protocol = "tcp"
      ports    = ["22","1521","4443","7001","7002","9001"]
    }]
    deny       = []
    log_config = null
  },
  {
        name               = "fw-qa-in-allow-mvt-office-vpn"
        network            = "vpc-qa-01"
        direction          = "INGRESS"
        description        = "allow INGRESS from MVT Office VPN to QA VPC"
        source_tags        = []
        source_ranges      = ["*************/24","*************/24"]
        destination_ranges = ["*********/16"]
        target_tags        = []
        priority           = 61000
        allow = [{
          protocol = "tcp"
          ports    = ["22","1521","4443","7001","7002","9001"]
        }]
        deny = []
  },
  {
    name               = "fw-qa-in-allow-azure-nonprod-hub-vpn"
    network            = "vpc-qa-01"
    direction          = "INGRESS"
    description        = "allow INGRESS from Azure NonProd Hub IH VPC VM subnet to QA VPC"
    source_ranges      = ["**********/16","**********/16","**********/16","**********/16","*********/16","*********/16","*********/16","*********/16"]
    destination_ranges = ["*********/16"]
    priority           = 61000
    target_tags        = []
    allow = [{
      protocol = "tcp"
      ports    = ["22","1521","4443","7001","7002","9001"]
    }]
    deny       = []
    log_config = null
  },
  {
    name               = "fw-qa-in-allow-azure-nonprod-hub-ih-vpn"
    network            = "vpc-qa-01"
    direction          = "INGRESS"
    description        = "allow INGRESS from Azure NonProd Hub IH VPC VM subnet to QA VPC"
    source_ranges      = ["**********/16","**********/16","**********/16"]
    destination_ranges = ["*********/16"]
    priority           = 61000
    target_tags        = []
    allow = [{
      protocol = "tcp"
      ports    = ["22","1521","4443","7001","7002","9001"]
    }]
    deny       = []
    log_config = null
  }
]

vpn_gcp_azure_config = {
  "nonprod-hub" = {
    gcp_vpc_name = "hub-nonprod-01"
    gcp_router_interface1_ip_range = "************/30"
    gcp_router_peer_ip_address1 = "************"
    azure_vpn_gateway_public_ip_1 = "*************"
    gcp_router_interface2_ip_range = "************/30"
    gcp_router_peer_ip_address2 = "************"
    shared_key = "3%XNixjI#bg8321458!!)"
  }
    "nonprod-hub-ih" = {
    gcp_vpc_name = "hub-nonprod-01"
    gcp_router_interface1_ip_range = "************/30"
    gcp_router_peer_ip_address1 = "************"
    azure_vpn_gateway_public_ip_1 = "************"
    gcp_router_interface2_ip_range = "************/30"
    gcp_router_peer_ip_address2 = "************"
    shared_key = "3%XNixjI#bg8321458!!)"
    }

}
