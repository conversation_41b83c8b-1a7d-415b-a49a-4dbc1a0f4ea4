project_name = "proj-nonprod-host-01"
enable_apis  = []
project_labels = {
  environment = "nonprod"
}


environment  = ["nonprod", "dev", "qa", "demo", "devops"]
network_config = {
  "nonprod/01" = {
    primary_cidr_range = "***********/28"
  }
  "sun/dev/01" = {
    primary_cidr_range = "*********/28"
    subnet_allowed_list = ["proj-dev-sun-01"]
    # psa_cidr = "<range>"
  }
  "moon/dev/01" = {
    primary_cidr_range = "**********/28"
  }
}

vpc_peering = {
  "nonprod-dev" = {
    network      = "dev"
    peer_network = "nonprod"
  }
  "nonprod-qa" = {
    network      = "qa"
    peer_network = "nonprod"
  }
  "nonprod-demo" = {
    network      = "demo"
    peer_network = "nonprod"
  }
  "nonprod-devops" = {
    network      = "devops"
    peer_network = "nonprod"
  }
}

customer_specific_rules = [

  ##### hub-nonprod-01 #####
  {
    name               = "fw-nonprod-eg-allow-internal"
    network            = "hub-nonprod-01"
    direction          = "EGRESS"
    description        = "allow EGRESS within MVT NonProd Hub VPC VM subnet"
    source_ranges      = ["***********/28"]
    destination_ranges = ["***********/28"]
    priority           = 60000
    target_tags        = []
    allow = [{
      protocol = "all"
      ports    = []
    }]
    deny       = []
    log_config = null
  },
  {
    name               = "fw-nonprod-ig-allow-internal"
    network            = "hub-nonprod-01"
    direction          = "INGRESS"
    description        = "allow INGRESS within MVT NonProd Hub VPC VM subnet"
    source_ranges      = ["***********/28"]
    destination_ranges = ["***********/28"]
    priority           = 59000
    target_tags        = []
    allow = [{
      protocol = "all"
      ports    = []
    }]
    deny       = []
    log_config = null
  },
  {
    name               = "fw-dev-eg-allow-internal-sun"
    network            = "vpc-dev-01"
    direction          = "EGRESS"
    description        = "allow EGRESS within sun"
    source_ranges      = ["*********/28"]
    destination_ranges = ["*********/28"]
    priority           = 60000
    target_tags        = []
    allow = [{
      protocol = "all"
      ports    = []
    }]
    deny       = []
    log_config = null
  },
  {
    name               = "fw-dev-in-allow-internal-sun"
    network            = "vpc-dev-01"
    direction          = "INGRESS"
    description        = "allow INGRESS within sun"
    source_ranges      = ["*********/28"]
    destination_ranges = ["*********/28"]
    priority           = 59000
    target_tags        = []
    allow = [{
      protocol = "all"
      ports    = []
    }]
    deny       = []
    log_config = null
  },
  {
    name               = "fw-dev-eg-allow-internal-moon"
    network            = "vpc-dev-01"
    direction          = "EGRESS"
    description        = "allow EGRESS within moon"
    source_ranges      = ["**********/28"]
    destination_ranges = ["**********/28"]
    priority           = 60000
    target_tags        = []
    allow = [{
      protocol = "all"
      ports    = []
    }]
    deny       = []
    log_config = null
  },
  {
    name               = "fw-dev-in-allow-internal-moon"
    network            = "vpc-dev-01"
    direction          = "INGRESS"
    description        = "allow INGRESS within moon"
    source_ranges      = ["**********/28"]
    destination_ranges = ["**********/28"]
    priority           = 59000
    target_tags        = []
    allow = [{
      protocol = "all"
      ports    = []
    }]
    deny       = []
    log_config = null
  }
]
