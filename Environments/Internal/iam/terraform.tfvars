org_id = "373017380746"

# Organization-level IAM configurations
org_iam_config = {
  "gcp-org-admins" = {
    member = "group:<EMAIL>"
    roles  = [
      "roles/resourcemanager.organizationAdmin",
      "roles/resourcemanager.folderAdmin",
      "roles/resourcemanager.projectCreator",
      "roles/iam.organizationRoleAdmin",
      "roles/serviceusage.serviceUsageAdmin",
      "roles/cloudsupport.techSupportEditor",
      "roles/essentialcontacts.admin",
      "roles/billing.user"
    ]
  }
  "gcp-org-viewers" = {
    member = "group:<EMAIL>"
    roles  = [
      "roles/resourcemanager.organizationViewer",
      "roles/browser",
      "roles/resourcemanager.folderViewer",
      "roles/serviceusage.serviceUsageViewer",
      "roles/iam.organizationRoleViewer"
    ]
  }
  "gcp-security-admins" = {
    member = "group:<EMAIL>"
    roles  = ["roles/iam.securityAdmin"]
  }
  "gcp-logging-admins" = {
    member = "group:<EMAIL>"
    roles  = ["roles/logging.admin"]
  }
  "gcp-logging-viewers" = {
    member = "group:<EMAIL>"
    roles  = ["roles/logging.viewer"]
  }
  "gcp-monitoring-admins" = {
    member = "group:<EMAIL>"
    roles  = ["roles/monitoring.admin"]
  }
  "searce-dba" = {
    member = "group:<EMAIL>"
    roles  = [
      "roles/resourcemanager.organizationViewer",
      "roles/compute.admin",
      "roles/iap.tunnelResourceAccessor",
      "roles/storage.admin",
      "roles/iam.serviceAccountUser"
    ]
  }
}

# Folder-level IAM configurations (for host projects)
folder_iam_config = {
#   "googlnetwork-admins" = {
#     folder = "************"
#     roles = [
#       "roles/compute.networkAdmin",
#       "roles/compute.loadBalancerAdmin",
#       "roles/dns.admin",
#       "roles/networkmanagement.admin",
#       "roles/recommender.firewallAdmin",
#       "roles/servicemanagement.quotaViewer"
#     ]
#   }
}

# Project-level IAM configurations (if needed for specific projects)
project_iam_config = {
  "gcp-network-admins-nonprod-host" = {
    project = "mvtech-proj-nonprod-host-01" // NonProd host project
    member = "group:<EMAIL>"
    roles = [
      "roles/compute.networkAdmin",
      "roles/compute.loadBalancerAdmin",
      "roles/dns.admin",
      "roles/networkmanagement.admin",
      "roles/recommender.firewallAdmin",
      "roles/servicemanagement.quotaViewer"
    ]
  }
  "gcp-network-admins-prod-host" = {
    project = "mvtech-proj-prod-host-01" // Prod host project
    member = "group:<EMAIL>"
    roles = [
      "roles/compute.networkAdmin",
      "roles/compute.loadBalancerAdmin",
      "roles/dns.admin",
      "roles/networkmanagement.admin",
      "roles/recommender.firewallAdmin",
      "roles/servicemanagement.quotaViewer"
    ]
  }
}
