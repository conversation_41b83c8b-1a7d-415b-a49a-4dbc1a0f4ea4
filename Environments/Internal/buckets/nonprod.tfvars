project_name = "proj-nonprod-shared-01"

bucket_configs = {
  "storage-nonprod-installer-01" = {
    location                    = "europe-west2"
    storage_class               = "STANDARD"
    public_access_prevention    = "enforced"
    uniform_bucket_level_access = true
    iam_members = [
      {
        role    = "roles/storage.objectViewer"
        members = ["serviceAccount:<EMAIL>",
        "serviceAccount:<EMAIL>"]
      }
    ]
  }
}
