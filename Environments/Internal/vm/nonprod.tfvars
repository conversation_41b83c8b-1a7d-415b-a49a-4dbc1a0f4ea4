project_name = "proj-nonprod-shared-01"
env          = "nonprod"

vms = {
  "vm-nonprod-jumphost-01" = {
    zone              = "europe-west2-a"
    machine_type      = "e2-custom-2-4096"
    resource_policies = ["daily-instance-schedule-policy"]
    boot_disk_size    = 50
    boot_disk_type    = "pd-balanced"
    boot_disk_image   = "windows-server-2025-dc-v20250710"
    tags              = ["iap", "jumphost", "egress-internet"]
    labels = {
      environment = "nonprod"
      application = "jumphost"
    }
  }
}

# #### VMs from Machine Image #####

# vms_from_machine_image = {
#   "db-dev12" = {
#     machine_image_name = "oracle-image"
#     machine_image_project_id = "mvtech-proj-dev-sun-01" //when image is in different project
#     #service_account_email = "" //when different sa needs to be attached to the vm then the vm-default one
#     labels = {
#       application       = "db-dev"
#       install-ops-agent = "yes" // to install ops agent on this VM
#     }
#     deletion_protection = false
#     metadata = {
#       enable-osconfig = "TRUE"
#     }
#     attached_disks = []
#   }
#   "db-pro12" = {
#     machine_image_name = "oracle-image-for-db-pro12"
#     machine_image_project_id = "mvtech-proj-dev-moon-01" //when image is in different project
#     #service_account_email = "" //when different sa needs to be attached to the vm then the vm-default one
#     labels = {
#       application       = "db-pro"
#       install-ops-agent = "yes" // to install ops agent on this VM
#     }
#     deletion_protection = false
#     metadata = {
#       enable-osconfig = "TRUE"
#     }
#     attached_disks = []
#   }
# }

