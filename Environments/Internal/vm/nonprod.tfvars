project_name = "proj-nonprod-shared-01"
env          = "nonprod"

vms = {
  "vm-nonprod-jumphost-01" = {
    zone              = "europe-west2-a"
    machine_type      = "e2-custom-2-4096"
    resource_policies = ["daily-instance-schedule-policy"]
    boot_disk_size    = 50
    boot_disk_type    = "pd-balanced"
    boot_disk_image   = "windows-server-2025-dc-v20250710"
    tags              = ["iap", "jumphost", "egress-internet"]
    labels = {
      environment = "nonprod"
      application = "jumphost"
    }
  }
}
