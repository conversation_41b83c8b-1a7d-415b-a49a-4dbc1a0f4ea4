variable "project_name" {
  type        = string
  description = "Project Name"
  default     = null
}

variable "resource_policies" {
  description = "Map of resource policies to create"
  type = map(object({
    region      = string
    description = optional(string, null)
    instance_schedule_policy = optional(object({
      vm_start_schedule = string
      vm_stop_schedule  = string
      time_zone         = string
    }), null)
  }))
}
