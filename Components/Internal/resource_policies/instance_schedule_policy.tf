######################################
##### Instance Start/Stop Policy #####
######################################

module "instance_schedule_policy" {
  source = "../../../modules/resource_policy"

  for_each = var.resource_policies

  policy_name              = each.key
  project_id               = "mvtech-${var.project_name}"
  region                   = each.value.region
  description              = each.value.description
  instance_schedule_policy = lookup(each.value, "instance_schedule_policy", null)
}
