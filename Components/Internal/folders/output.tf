###################
##### Outputs #####
###################

output "fldr_mvt_id" {
  value = module.folders["root_folders"].folder_ids["fldr-mvt"]
}

output "fldr_automation_id" {
  value = module.folders["env_folders"].folder_ids["fldr-automation"]
}

output "fldr_shared_id" {
  value = module.folders["env_folders"].folder_ids["fldr-shared"]
}

output "fldr_nonprod_id" {
  value = module.folders["env_folders"].folder_ids["fldr-nonprod"]
}

output "fldr_prod_id" {
  value = module.folders["env_folders"].folder_ids["fldr-prod"]
}

output "fldr_dev_id" {
  value = module.folders["nonprod_folders"].folder_ids["fldr-dev"]
}

output "fldr_qa_id" {
  value = module.folders["nonprod_folders"].folder_ids["fldr-qa"]
}

output "fldr_demo_id" {
  value = module.folders["nonprod_folders"].folder_ids["fldr-demo"]
}

output "fldr_devops_id" {
  value = module.folders["nonprod_folders"].folder_ids["fldr-devops"]
}

output "fldr_cuat_id" {
  value = module.folders["prod_folders"].folder_ids["fldr-cuat"]
}

output "fldr_live_id" {
  value = module.folders["prod_folders"].folder_ids["fldr-live"]
}