########################
##### Host Project #####
########################

module "host_project" {
  source = "../../../modules/project"

  project_name        = var.project_name
  folder_id           = data.terraform_remote_state.folders.outputs.fldr_shared_id
  billing_account     = var.billing_account
  enable_host_project = true
  labels              = var.project_labels
  enable_apis = concat(
    [
      "compute.googleapis.com",
      "iam.googleapis.com",
      "stackdriver.googleapis.com",
      "iamcredentials.googleapis.com",
      "dns.googleapis.com",
      "servicenetworking.googleapis.com",
      "networkmanagement.googleapis.com",
      "iap.googleapis.com",
      "domains.googleapis.com",
      "certificatemanager.googleapis.com",
      "privateca.googleapis.com"
    ],
    var.enable_apis
  )
}
