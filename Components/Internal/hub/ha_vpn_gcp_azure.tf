module "cmn_ha_vpn_gcp_azure" {
  source                         = "../../../modules/ha_vpn_gcp_azure"
  for_each                       = var.vpn_gcp_azure_config
  gcp_project_id                 = "mvtech-${var.project_name}"
  gcp_region                     = var.region
  purpose                        = each.key
  gcp_vpc_name                   = each.value.gcp_vpc_name   # <- must be set!
  gcp_router_interface1_ip_range = each.value.gcp_router_interface1_ip_range
  gcp_router_interface2_ip_range = each.value.gcp_router_interface2_ip_range
  gcp_router_peer_ip_address1    = each.value.gcp_router_peer_ip_address1
  gcp_router_peer_ip_address2    = each.value.gcp_router_peer_ip_address2
  azure_vpn_gateway_public_ip_1  = each.value.azure_vpn_gateway_public_ip_1
  shared_key                     = each.value.shared_key
}