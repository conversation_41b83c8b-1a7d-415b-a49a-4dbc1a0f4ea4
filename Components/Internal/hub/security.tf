##########################
##### Firewall Rules #####
##########################

##### Common rules - applied to each environment
module "common_firewall_rules" {
  source     = "../../../modules/firewall"
  project_id = "mvtech-${var.project_name}"
  for_each   = toset(var.environment)
  common_rules = [
    {
      name               = "fw-${each.value}-eg-deny-all"
      network            = each.value == "prod" || each.value == "nonprod" ? "hub-${each.value}-01" : "vpc-${each.value}-01"
      direction          = "EGRESS"
      description        = "default deny all EGRESS"
      priority           = 65000
      source_ranges      = ["0.0.0.0/0"]
      destination_ranges = ["0.0.0.0/0"]
      source_tags        = []
      target_tags        = []
      allow              = []
      deny = [{
        protocol = "all"
        ports    = []
      }]
    },
    {
      name               = "fw-${each.value}-in-allow-iap"
      network            = each.value == "prod" || each.value == "nonprod" ? "hub-${each.value}-01" : "vpc-${each.value}-01"
      direction          = "INGRESS"
      description        = "For IAP SSH and RDP"
      source_ranges      = ["************/20"]
      destination_ranges = []
      source_tags        = []
      target_tags        = ["iap"]
      priority           = 64000
      allow = [{
        protocol = "tcp"
        ports    = ["22", "3389"]
      }]
      deny = []
    },
    {
      name               = "fw-${each.value}-in-allow-lbheathchk"
      network            = each.value == "prod" || each.value == "nonprod" ? "hub-${each.value}-01" : "vpc-${each.value}-01"
      direction          = "INGRESS"
      description        = "Allow Ingress from Load Balancer Health Check IP Ranges"
      source_ranges      = ["**********/16", "***********/22", "************/22", "************/22"]
      destination_ranges = []
      source_tags        = []
      target_tags        = ["http-lb"]
      priority           = 62000
      allow = [{
        protocol = "tcp"
        ports    = ["443"]
      }]
      deny = []
    }
  ]
}

##### Customer-specific rules (network-specific)
module "customer_specific_firewall_rules" {
  source     = "../../../modules/firewall"
  project_id = "mvtech-${var.project_name}"
  customer_specific_rules = concat(split("-", var.project_name)[1] == "prod" ?

    ##### Prod Host Project Firewall Rules #####

    [{
      name               = "fw-prod-eg-allow-internet-all"
      network            = "hub-prod-01"
      direction          = "EGRESS"
      description        = "default allow EGRESS from MVT Prod Hub VPC"
      source_tags        = []
      source_ranges      = ["*********/17"]
      destination_ranges = ["0.0.0.0/0"]
      target_tags        = ["egress-internet"]
      priority           = 64000
      allow = [{
        protocol = "tcp"
        ports    = ["443"]
      }]
      deny = []
      },
      {
        name               = "fw-prod-eg-allow-jumphost"
        network            = "hub-prod-01"
        direction          = "EGRESS"
        description        = "allow EGRESS from Prod Hub VPC VM subnet to CUAT and LIVE VPC"
        source_tags        = []
        source_ranges      = ["*********/28"]
        destination_ranges = ["*********/16", "*********/16"]
        target_tags        = ["jumphost"]
        priority           = 61000
        allow = [{
          protocol = "all"
          ports    = []
        }]
        deny = []
      },
      {
        name               = "fw-cuat-eg-allow-internet-all"
        network            = "vpc-cuat-01"
        direction          = "EGRESS"
        description        = "default allow EGRESS from MVT CUAT VPC"
        source_tags        = []
        source_ranges      = ["*********/16"]
        destination_ranges = ["0.0.0.0/0"]
        target_tags        = ["egress-internet"]
        priority           = 64000
        allow = [{
          protocol = "tcp"
          ports    = ["443"]
        }]
        deny = []
      },
      {
        name               = "fw-cuat-ig-allow-jumphost"
        network            = "vpc-cuat-01"
        direction          = "INGRESS"
        description        = "allow INGRESS from Prod Hub VPC VM subnet to CUAT  VPC"
        source_tags        = ["jumphost"]
        source_ranges      = ["*********/28"]
        destination_ranges = ["*********/16"]
        target_tags        = []
        priority           = 61000
        allow = [{
          protocol = "all"
          ports    = []
        }]
        deny = []
      },
      {
        name               = "fw-live-eg-allow-internet-all"
        network            = "vpc-live-01"
        direction          = "EGRESS"
        description        = "default allow EGRESS from MVT LIVE VPC"
        source_tags        = []
        source_ranges      = ["*********/16"]
        destination_ranges = ["0.0.0.0/0"]
        target_tags        = ["egress-internet"]
        priority           = 64000
        allow = [{
          protocol = "tcp"
          ports    = ["443"]
        }]
        deny = []
      },
      {
        name               = "fw-live-ig-allow-jumphost"
        network            = "vpc-live-01"
        direction          = "INGRESS"
        description        = "allow INGRESS from Prod Hub VPC VM subnet to LIVE VPC"
        source_tags        = ["jumphost"]
        source_ranges      = ["*********/28"]
        destination_ranges = ["*********/16"]
        target_tags        = []
        priority           = 61000
        allow = [{
          protocol = "all"
          ports    = []
        }]
        deny = []
      }
    ]

    :

    ##### NonProd Host Project Firewall Rules #####

    [{
      name               = "fw-nonprod-eg-allow-internet-all"
      network            = "hub-nonprod-01"
      direction          = "EGRESS"
      description        = "default allow EGRESS from MVT NonProd Hub VPC"
      source_tags        = []
      source_ranges      = ["***********/17"]
      destination_ranges = ["0.0.0.0/0"]
      target_tags        = ["egress-internet"]
      priority           = 64000
      allow = [{
        protocol = "tcp"
        ports    = ["443"]
      }]
      deny = []
      },
      {
        name               = "fw-nonprod-eg-allow-jumphost"
        network            = "hub-nonprod-01"
        direction          = "EGRESS"
        description        = "allow EGRESS from NonProd Hub VPC VM subnet to Dev, Demo, DevOps and QA VPC"
        source_tags        = []
        source_ranges      = ["***********/28"]
        destination_ranges = ["*********/16", "*********/16", "*********/16", "*********/16"]
        target_tags        = ["jumphost"]
        priority           = 61000
        allow = [{
          protocol = "all"
          ports    = []
        }]
        deny = []
      },
      {
        name               = "fw-dev-eg-allow-internet-all"
        network            = "vpc-dev-01"
        direction          = "EGRESS"
        description        = "default allow EGRESS from MVT DEV VPC"
        source_tags        = []
        source_ranges      = ["*********/16"]
        destination_ranges = ["0.0.0.0/0"]
        target_tags        = ["egress-internet"]
        priority           = 64000
        allow = [{
          protocol = "tcp"
          ports    = ["443"]
        }]
        deny = []
      },
      {
        name               = "fw-dev-ig-allow-jumphost"
        network            = "vpc-dev-01"
        direction          = "INGRESS"
        description        = "allow INGRESS from NonProd Hub VPC VM subnet to Dev VPC"
        source_tags        = ["jumphost"]
        source_ranges      = ["***********/28"]
        destination_ranges = ["*********/16"]
        target_tags        = []
        priority           = 61000
        allow = [{
          protocol = "all"
          ports    = []
        }]
        deny = []
      },
      {
        name               = "fw-demo-eg-allow-internet-all"
        network            = "vpc-demo-01"
        direction          = "EGRESS"
        description        = "default allow EGRESS from MVT DEMO VPC"
        source_tags        = []
        source_ranges      = ["*********/16"]
        destination_ranges = ["0.0.0.0/0"]
        target_tags        = ["egress-internet"]
        priority           = 64000
        allow = [{
          protocol = "tcp"
          ports    = ["443"]
        }]
        deny = []
      },
      {
        name               = "fw-demo-ig-allow-jumphost"
        network            = "vpc-demo-01"
        direction          = "INGRESS"
        description        = "allow INGRESS from NonProd Hub VPC VM subnet to Demo VPC"
        source_tags        = ["jumphost"]
        source_ranges      = ["***********/28"]
        destination_ranges = ["*********/16"]
        target_tags        = []
        priority           = 61000
        allow = [{
          protocol = "all"
          ports    = []
        }]
        deny = []
      },
      {
        name               = "fw-devops-eg-allow-internet-all"
        network            = "vpc-devops-01"
        direction          = "EGRESS"
        description        = "default allow EGRESS from MVT DevOps VPC"
        source_tags        = []
        source_ranges      = ["*********/16"]
        destination_ranges = ["0.0.0.0/0"]
        target_tags        = ["egress-internet"]
        priority           = 64000
        allow = [{
          protocol = "tcp"
          ports    = ["443"]
        }]
        deny = []
      },
      {
        name               = "fw-devops-ig-allow-jumphost"
        network            = "vpc-devops-01"
        direction          = "INGRESS"
        description        = "allow INGRESS from NonProd Hub VPC VM subnet to DevOps VPC"
        source_tags        = ["jumphost"]
        source_ranges      = ["***********/28"]
        destination_ranges = ["*********/16"]
        target_tags        = []
        priority           = 61000
        allow = [{
          protocol = "all"
          ports    = []
        }]
        deny = []
      },
      {
        name               = "fw-qa-eg-allow-internet-all"
        network            = "vpc-qa-01"
        direction          = "EGRESS"
        description        = "default allow EGRESS from MVT QA VPC"
        source_tags        = []
        source_ranges      = ["*********/16"]
        destination_ranges = ["0.0.0.0/0"]
        target_tags        = ["egress-internet"]
        priority           = 64000
        allow = [{
          protocol = "tcp"
          ports    = ["443"]
        }]
        deny = []
      },
      {
        name               = "fw-qa-ig-allow-jumphost"
        network            = "vpc-qa-01"
        direction          = "INGRESS"
        description        = "allow INGRESS from NonProd Hub VPC VM subnet to QA VPC"
        source_tags        = ["jumphost"]
        source_ranges      = ["***********/28"]
        destination_ranges = ["*********/16"]
        target_tags        = []
        priority           = 61000
        allow = [{
          protocol = "all"
          ports    = []
        }]
        deny = []
  }], var.customer_specific_rules)
}
