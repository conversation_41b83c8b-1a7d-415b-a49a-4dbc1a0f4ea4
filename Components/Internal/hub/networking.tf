###################################
######## Google Networking ########
###################################

module "networking" {
  source         = "../../../modules/networking"
  project_id     = "mvtech-${var.project_name}"
  environment    = var.environment
  region         = var.region
  network_config = var.network_config
  region_prefix  = var.region_prefix
  vpc_peering    = var.vpc_peering
}

/******************************************
  Project policy, allow values (list constraint)
 *****************************************/
resource "google_org_policy_policy" "primary" {
  for_each = toset(flatten([
    for k, v in var.network_config : v.subnet_allowed_list
    if length(v.subnet_allowed_list) > 0
  ]))

  name   = "projects/mvtech-${each.value}/policies/compute.restrictSharedVpcSubnetworks"
  parent = "projects/mvtech-${each.value}"

  spec {
    rules {
      values {
        allowed_values = [
          for subnet_key, subnet_config in var.network_config :
          "projects/mvtech-${var.project_name}/regions/${var.region}/subnetworks/${
            split("/", subnet_key)[0] == "prod" || split("/", subnet_key)[0] == "nonprod"
            ? "snet-${split("/", subnet_key)[0]}-vm-${split("/", subnet_key)[1]}"
            : "snet-${split("/", subnet_key)[1]}-${split("/", subnet_key)[0]}-vm-${split("/", subnet_key)[2]}"
          }"
          if contains(subnet_config.subnet_allowed_list, each.value)
        ]
      }
    }
  }
}