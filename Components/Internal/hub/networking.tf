###################################
######## Google Networking ########
###################################

module "networking" {
  source         = "../../../modules/networking"
  project_id     = "mvtech-${var.project_name}"
  environment    = var.environment
  region         = var.region
  network_config = var.network_config
  region_prefix  = var.region_prefix
  vpc_peering    = var.vpc_peering
}

/******************************************
  Project policy, allow values (list constraint)
 *****************************************/
resource "google_org_policy_policy" "primary" {
  for_each = toset(var.network_config)
  name   = "projects/mvtech-${var.network_config[each.key].subnet_allowed_list}/policies/compute.restrictSharedVpcSubnetworks"
  parent = "projects/mvtech-${var.network_config[each.key].subnet_allowed_list}"

  spec {
    rules {
      values {
        allowed_values = "projects/mvtech-${var.project_name}/regions/${var.region}/subnetworks/${split("/", each.key)[1]}-${split("/", each.key)[0]}-vm-${split("/", each.key)[2]}"
      }
    }
  }
}