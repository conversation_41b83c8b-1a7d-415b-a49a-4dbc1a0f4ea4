variable "project_name" {
  type        = string
  description = "Project name"
  default     = ""
}

variable "enable_apis" {
  type        = list(string)
  description = "List of APIs that needs to be enabled on the project."
  default     = []
}

variable "billing_account" {
  type        = string
  description = "Billing Account ID"
  default     = "01EF67-9CAB59-5DD427"
}

variable "project_labels" {
  type        = map(string)
  description = "A set of key/value label pairs to assign to the project"
  default     = null
}

variable "environment" {
  type        = list(string)
  description = "Environments to be deployed"
  default     = [""]
}

variable "region" {
  type        = string
  default     = "europe-west2"
  description = "Region for the deployment of resources"
}


variable "network_config" {
  type = map(object({
    primary_cidr_range       = string
    private_ip_google_access = optional(bool, true)
    psa_cidr                 = optional(string, "")
    # secondary_cidr_ranges = optional(list(object({
    #   range_name    = string
    #   ip_cidr_range = string
    # })))
    subnet_allowed_list = optional(list(string), [])

  }))
  description = "Configuration for the subnets."
  default     = null
}

variable "region_prefix" {
  type        = string
  description = "region prefix for the resources"
  default     = "ew2"
}

variable "vpc_peering" {
  type = map(object({
    network      = string
    peer_network = string
  }))
  description = "vpc peering configuration"
  default     = null
}

variable "common_rules" {
  type = list(object({
    name                    = string
    description             = optional(string)
    direction               = string
    priority                = number
    network                 = string
    source_ranges           = optional(list(string))
    destination_ranges      = optional(list(string))
    source_tags             = optional(list(string))
    source_service_accounts = optional(list(string))
    target_tags             = optional(list(string))
    target_service_accounts = optional(list(string))
    allow = list(object({
      protocol = string
      ports    = list(string)
    }))
    deny = optional(list(object({
      protocol = string
      ports    = list(string)
    })))
    log_config = optional(object({
      metadata = string
    }))
  }))
  description = "Common firewall rules that apply to all environments"
  default     = []
}

variable "customer_specific_rules" {
  type = list(object({
    name                    = string
    description             = optional(string)
    direction               = string
    priority                = number
    network                 = string
    source_ranges           = list(string)
    destination_ranges      = list(string)
    source_tags             = optional(list(string))
    source_service_accounts = optional(list(string))
    target_tags             = optional(list(string))
    target_service_accounts = optional(list(string))
    allow = list(object({
      protocol = string
      ports    = list(string)
    }))
    deny = optional(list(object({
      protocol = string
      ports    = list(string)
    })))
    log_config = optional(object({
      metadata = string
    }))
  }))
  description = "Environment-specific firewall rules to be applied"
  default     = []
}
