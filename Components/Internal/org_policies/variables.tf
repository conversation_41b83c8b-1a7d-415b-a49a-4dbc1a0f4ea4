variable "parent" {
  type        = string
  description = "The parent can be at Org level or folder level"
  default     = ""
}

variable "iam_domain_restricted_sharing" {
  type        = list(string)
  description = "List of domains to be allowed to get access in IAM policy which will be enforced through the Org Policy. Can be optional."
  default     = [""]
}

variable "iam_allowed_contact_domains" {
  type        = list(string)
  description = "List of allowed domains can be added in Essential Contacts. which will be enforced through the Org Policy."
}

variable "iam_disableServiceAccountKeyCreation" {
  type        = bool
  description = "This boolean constraint disables the creation of service account external keys"
  default     = true
}

variable "iam_disableServiceAccountKeyUpload" {
  type        = bool
  description = "It will not allow to upload any Service Account key"
  default     = true
}

variable "iam_automaticIamGrantsForDefaultServiceAccounts" {
  type        = bool
  description = "Prevents the default App Engine and Compute Engine service accounts that are created in your projects from being automatically granted any IAM role on the project when the accounts are created."
  default     = true
}

variable "org_vm_external_ip_access" {
  type        = list(string)
  description = "Provide list of VM's to exclude from the policy"
  default     = []
}

variable "compute_disableVpcExternalIpv6" {
  type        = bool
  description = "If set true, this constraint prevents from configuring dual-stack subnets with external IPv6 ranges."
  default     = true
}

variable "compute_disableNestedVirtualization" {
  type        = bool
  description = "If true it will disable nested virtulization."
  default     = true
}

variable "org_restrict_protocol_forwarding_creation_for_types" {
  type        = list(string)
  description = "When you set the compute.restrictProtocolForwardingCreationForTypes constraint, you specify an allowlist or denylist of the protocol forwarding types. The list of allowed or denied values can only include values from the following list: 'INTERNAL''EXTERNAL'."
}

variable "sql_restrictPublicIp" {
  type        = bool
  description = "Restricts configuring Public IP on Cloud SQL instances where this constraint is set to True."
  default     = true
}

variable "sql_restrictAuthorizedNetworks" {
  type        = bool
  description = "Restricts adding Authorized Networks for unproxied database access to Cloud SQL instances where this constraint is set to True."
  default     = true
}

variable "storage_bucket_publicAccessPrevention" {
  type        = bool
  description = "Prevents Public access on the Storage bucket"
  default     = true
}

variable "storage_uniformBucketLevelAccess" {
  type        = bool
  description = "If true, Any new bucket in the Organization resource must have uniform bucket-level access enabled."
  default     = true
}

variable "compute_network_restrictXpnProjectLienRemoval" {
  type        = bool
  description = ""
  default     = true
}

variable "compute_network_skipDefaultNetworkCreation" {
  type        = bool
  description = "If true this will skip default vpc creation while creating new project."
  default     = true
}

variable "gcp_restrictResourceLocations" {
  type        = list(string)
  description = "restrict the locations"
  default     = ["in:europe-west1-locations"]
}


variable "domains_to_allow" {
  type        = list(string)
  description = "value"
  default     = [""]
}

variable "compute_disableSerialPortAccess" {
  type        = bool
  description = "Disable Serial Port Access"
  default     = true
}