# Project IAM - Multiple roles for multiple members
module "project_iam" {
  source = "../../../modules/iam"
  parent = "project"

  for_each = var.project_iam_config
  project  = each.value.project
  members_roles = {
    (each.key) = {
      member = each.value.member
      roles = each.value.roles
    }
  }
  }


# Folder IAM - Multiple roles for multiple members
module "folder_iam" {
  source = "../../../modules/iam"
  parent = "folder"

  for_each  = var.folder_iam_config
  folder_id = each.value.folder
  members_roles = {
    (each.key) = {
      member = each.value.member
      roles = each.value.roles
    }
  }
}

# Organization IAM - Multiple roles for multiple members
module "org_iam" {
  source = "../../../modules/iam"
  parent = "organization"
  org_id = var.org_id
  
  for_each = var.org_iam_config
  members_roles = {
    (each.key) = {
      member = each.value.member
      roles = each.value.roles
    }
  }
}
