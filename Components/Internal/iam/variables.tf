variable "org_id" {
  type        = string
  default     = ""
  description = "Organization ID for IAM assignments"
}

variable "project_iam_config" {
  type = map(object({
    project = string
    member = string
    roles   = list(string)
  }))
  default = {}
}

variable "folder_iam_config" {
  type = map(object({
    folder = string
    member = string
    roles  = list(string)
  }))
  default = {}
}

variable "org_iam_config" {
  type = map(object({
    member = string
    roles  = list(string)
  }))
  default = {}
}
