#####################
#####  Projects #####
#####################

module "project" {
  source = "../../../modules/project"

  project_name    = var.project_name
  environment     = var.env
  folder_id       = data.terraform_remote_state.folders.outputs["fldr_${var.env}_id"]
  billing_account = var.billing_account
  host_project_id = var.host_project_id
  create_sa       = var.create_sa
  labels          = merge(
    {
      environment = var.env
    },
    var.project_labels
  )

  enable_apis = concat(
    [
      "compute.googleapis.com",
      "iam.googleapis.com",
      "stackdriver.googleapis.com",
      "iamcredentials.googleapis.com",
      "dns.googleapis.com",
      "servicenetworking.googleapis.com",
      "networkmanagement.googleapis.com",
      "iap.googleapis.com",
      "domains.googleapis.com"
    ],
    var.enable_apis
  )
}
