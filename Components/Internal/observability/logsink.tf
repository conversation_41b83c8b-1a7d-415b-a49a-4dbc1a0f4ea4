###################################
############ Log Sink #############
###################################

locals {
  logging_sink_filter = <<EOF
     logName: /logs/cloudaudit.googleapis.com%2Factivity OR
     logName: /logs/cloudaudit.googleapis.com%2Fsystem_event OR
     logName: /logs/cloudaudit.googleapis.com%2Fdata_access OR
     logName: /logs/compute.googleapis.com%2Fpolicy OR
     logName: /logs/cloudaudit.googleapis.com%2Faccess_transparency
 EOF
}

###################################
########## Dev Log Sink ###########
###################################


module "dev_fldr_logs_export" {
  source = "../../../modules/logsink"
  for_each = toset(var.logsink)
  resources = {
    parent_id = data.terraform_remote_state.folders.outputs["fldr_${each.value}_id"]
  }
  resource_type                  = "folder"
  logging_destination_project_id = "mvtech-${var.project_name}"
  logbucket_options = {
    logging_sink_name   = "logsink-${each.value}-fldr"
    logging_sink_filter = local.logging_sink_filter
    name                = "logbkt-${each.value}-fldr"
  }
  depends_on = [ module.project ]
}