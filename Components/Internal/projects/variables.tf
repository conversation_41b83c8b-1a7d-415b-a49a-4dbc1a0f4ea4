variable "project_name" {
  type        = string
  description = "Project Name"
  default     = ""
}

variable "env" {
  type        = string
  description = "To define Environments such as cuat, live, dev, demo etc."
}

variable "host_project_id" {
  type        = string
  description = "Host Project ID"
  default     = ""
}

variable "create_sa" {
  type        = bool
  description = "Whether to create compute engine SA."
  default     = false
}

variable "project_labels" {
  type        = map(string)
  description = "A set of key/value label pairs to assign to the project"
  default     = null
}

variable "billing_account" {
  type        = string
  description = "Billing Account ID"
  default     = "01EF67-9CAB59-5DD427"
}

variable "enable_apis" {
  type        = list(string)
  description = "List of APIs that needs to be enabled on the project."
  default     = []
}