module "customer_vms" {
  source = "../../../modules/vm"

  count                 = length(var.vms) > 0 ? 1 : 0
  project_id            = "mvtech-${var.project_name}"
  network               = data.google_compute_network.vpc[0].self_link
  subnetwork            = data.google_compute_subnetwork.subnet[0].self_link
  subnetwork_project    = var.env == "cuat" || var.env == "live" || var.env == "prod" ? "mvtech-proj-prod-host-01" : "mvtech-proj-nonprod-host-01"
  service_account_email = "id-${var.env}-vm-default@mvtech-${var.project_name}.iam.gserviceaccount.com"

  vms = var.vms
}

