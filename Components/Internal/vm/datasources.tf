# Data sources to get network information for VM creation
data "google_compute_network" "vpc" {
  count   = length(var.vms) > 0 ? 1 : 0
  name    = var.env == "prod" || var.env == "nonprod" ? "hub-${var.env}-01" : "vpc-${var.env}-01"
  project = var.env == "cuat" || var.env == "live" || var.env == "prod" ? "mvtech-proj-prod-host-01" : "mvtech-proj-nonprod-host-01"
}

data "google_compute_subnetwork" "subnet" {
  count   = length(var.vms) > 0 ? 1 : 0
  name    = var.env == "prod" || var.env == "nonprod" ? "snet-${var.env}-vm-01" : "snet-${var.env}-${split("-", var.project_name)[2]}-vm-01"
  region  = var.region
  project = var.env == "cuat" || var.env == "live" ? "mvtech-proj-prod-host-01" : "mvtech-proj-nonprod-host-01"
}