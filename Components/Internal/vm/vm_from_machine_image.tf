###########################################
##### To Create VM from Machine Image #####
###########################################

module "vms_from_machine_image" {
  source = "../../../modules/vm_from_machine_image"

  project_id         = "mvtech-${var.project_name}"
  env                = var.env
  network            = data.google_compute_network.vpc[0].self_link
  subnetwork         = data.google_compute_subnetwork.subnet[0].self_link
  subnetwork_project = var.env == "cuat" || var.env == "live" ? "mvtech-proj-prod-host-01" : "mvtech-proj-nonprod-host-01"

  vms_from_machine_image = var.vms_from_machine_image

}
