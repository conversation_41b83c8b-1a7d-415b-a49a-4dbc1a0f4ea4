#############################
##### Resource Polocies #####
#############################

module "resource_policy" {
  source = "../../modules/resource_policy"

  for_each = var.resource_policies != null ? var.resource_policies : {}

  policy_name              = each.key
  project_id               = "mvtech-${var.project_name}"
  region                   = each.value.region
  description              = each.value.description
  instance_schedule_policy = lookup(each.value, "instance_schedule_policy", null)
  snapshot_schedule_policy = lookup(each.value, "snapshot_schedule_policy", null)

  depends_on = [ module.customer_service_project ]
}
