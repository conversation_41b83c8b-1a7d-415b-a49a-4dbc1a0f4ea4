
variable "project_name" {
  type        = string
  description = "Project Name"
  default     = ""
}

variable "env" {
  type        = string
  description = "To define Environments such as cuat, live, dev, demo etc."
}

variable "billing_account" {
  type        = string
  description = "Billing Account ID"
  default     = "01EF67-9CAB59-5DD427"
}

variable "project_labels" {
  type        = map(string)
  description = "A set of key/value label pairs to assign to the project"
  default     = null
}

variable "enable_apis" {
  type        = list(string)
  description = "List of APIs that needs to be enabled on the project."
  default     = []
}

# VM Configuration Variables
variable "vms" {
  description = "Map of VM configurations"
  type = map(object({
    zone                    = string
    machine_type            = string
    boot_disk_size          = number
    boot_disk_type          = string
    boot_disk_image         = string
    nic_type                = optional(string, null)
    assign_public_ip        = optional(bool, false)
    tags                    = optional(list(string), [])
    labels                  = optional(map(string), {})
    metadata_startup_script = optional(string)
    metadata                = optional(map(string), {})
    auto_delete             = optional(bool, true)
    deletion_protection     = optional(bool, true)
    network_ip              = optional(string)
    nat_ip                  = optional(string)
    service_account_scopes  = optional(list(string), ["https://www.googleapis.com/auth/cloud-platform"])
    attached_disks = optional(list(object({
      name = string
      mode = optional(string, "READ_WRITE")
      size = optional(number, 50)
      type = optional(string, "pd-standard")
    })), [])
  }))
  default = {}
}

variable "project_iam_config" {
  type = map(object({
    project = string
    member  = string
    roles   = list(string)
  }))
  default = {}
}

variable "region" {
  type        = string
  description = "Region of the subnet to fetch the datasource"
  default     = "europe-west2"
}

# Ops Agent Policy Variables

variable "create_ops_agent_policy" {
  type        = bool
  description = "Whether to create the ops agent policy."
  default     = false
}

variable "zone" {
  type        = string
  description = "Zone for the Zonal OS Policy"
  default     = "europe-west2-a"
}

##### Bucket Variables

variable "bucket_configs" {
  type = map(object({
    location                     = string
    purpose                      = optional(string, "general")
    force_destroy                = optional(bool, false)
    storage_class                = optional(string, "STANDARD")
    labels                       = optional(map(string), {})
    versioning                   = optional(bool, false)
    public_access_prevention     = optional(string, "enforced")
    uniform_bucket_level_access  = optional(bool, true)
    autoclass_enable             = optional(bool, false)
    soft_delete_retention_period = optional(number, 604800)
    lifecycle_rule = optional(list(object({
      age                        = optional(number)
      num_newer_versions         = optional(number)
      days_since_noncurrent_time = optional(number)
      action                     = string
      storage_class              = optional(string)
    })), [])
    retention_policy = optional(list(object({
      retention_period = optional(number)
    })), [])
    cors = optional(list(object({
      origin          = optional(string)
      method          = optional(string)
      response_header = optional(string)
      max_age_seconds = optional(number)
    })), [])
    website = optional(map(any), {})
    logging = optional(list(object({
      log_bucket        = string
      log_object_prefix = optional(string)
    })), [])
    iam_members = optional(list(object({
      role    = string
      members = list(string)
    })), [])
  }))
  description = "Map of bucket configurations to create"
  default     = {}
}

##### Machine Image Variables

#####################
##### Variables #####
#####################

variable "project_id" {
  type        = string
  description = "The Project ID"
  default     = null
}

variable "create_machine_image" {
  type        = bool
  description = "Whether to create a machine image from the source instance"
  default     = false
}

variable "machine_images" {
  type = map(object({
    source_instance = string
    zone            = optional(string, "europe-west2-a")
  }))
  default = {}
}

##### VM from Machine Image Variables

variable "vms_from_machine_image" {
  type = map(object({

    zone                     = optional(string, "europe-west2-a")
    machine_type             = optional(string)
    machine_image_name       = string
    machine_image_project_id = optional(string, "")
    deletion_protection      = optional(bool, true)
    labels                   = map(string)
    network_ip               = optional(string)
    nic_type                 = optional(string, null)
    assign_public_ip         = optional(bool, false)
    nat_ip                   = optional(string)
    service_account_email    = optional(string)
    service_account_scopes   = optional(list(string), ["https://www.googleapis.com/auth/cloud-platform"])
    metadata                 = optional(map(string), {})
    attached_disks = optional(list(object({
      name = string
      size = number
      type = string
      mode = string
    })), [])
  }))
  default = {}
}



# Custom Servoce Account Variables

variable "custom_service_accounts" {
  type = list(object({
    sa_purpose = string
  }))
  default = []
}