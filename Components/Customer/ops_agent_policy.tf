##########################################
##### OS Policy to install Ops Agent #####
##########################################

# This module will create an ops agent policy for the project if "create_ops_agent_policy" is set to true
module "ops_agent_policy" {
  source = "../../modules/resource_policy/ops_agent_policy"

  create_ops_agent_policy = var.create_ops_agent_policy
  policy_id               = "ops-agent-policy-${var.env}-${split("-", var.project_name)[2]}-01"
  project_id              = "mvtech-${var.project_name}"
  zone                    = var.zone
  labels = {
    install-ops-agent = "yes"
  }

  depends_on = [ module.customer_service_project ]
}

