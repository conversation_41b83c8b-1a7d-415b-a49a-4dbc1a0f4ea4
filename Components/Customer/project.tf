#####################################
##### Customer Service Projects #####
#####################################

module "customer_service_project" {
  source = "../../modules/project"

  project_name    = var.project_name
  environment     = var.env
  folder_id       = data.terraform_remote_state.folders.outputs["fldr_${var.env}_id"]
  billing_account = var.billing_account
  host_project_id = var.env == "cuat" || var.env == "live" ? "mvtech-proj-prod-host-01" : "mvtech-proj-nonprod-host-01"
  create_sa       = true
  labels          = merge(
    {
      environment = var.env
    },
    var.project_labels
  )
  enable_apis = concat(
    [
      "compute.googleapis.com",
      "iam.googleapis.com",
      "stackdriver.googleapis.com",
      "iamcredentials.googleapis.com",
      "dns.googleapis.com",
      "servicenetworking.googleapis.com",
      "networkmanagement.googleapis.com",
      "iap.googleapis.com",
      "domains.googleapis.com",
      "osconfig.googleapis.com"
    ],
    var.enable_apis
  )
}
