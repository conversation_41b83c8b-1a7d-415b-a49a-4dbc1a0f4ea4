trigger:
  branches:
    include:
      - develop
  paths:
    include:
      - 'Components/Customer/**'
      - 'Environments/Customer/dummy/**'

pr:
  branches:
    include:
      - '*'
  paths:
    include:
      - 'Components/Customer/**'
      - 'Environments/Customer/dummy/**'

parameters:
  - name: destroyTerraform
    type: boolean
    default: false
  - name: forceTerraform
    type: boolean
    default: false

variables:
  - group: 'GCP-Terraform'

stages:
  - template: ../../../templates/gcp-terraforming.tmpl.yml
    parameters:
      name: 'Terraform_Customer_Dummy_Dev'
      environment: 'Dev'
      workingDir: '$(System.DefaultWorkingDirectory)/Components/Customer'
      variableDirectory: '$(System.DefaultWorkingDirectory)/Environments/Customer/dummy/dev'
      azureServiceConnectionForWIF: $(AZURE_SERVICE_CONNECTION_WIF)
      backendGcsBucket: $(GCS_BACKEND_BUCKET)
      backendGcsPrefix: 'Customer/dummy/dev.tfstate'
      tfPlanName: 'dummy.dev.tfplan'
      tfDestroy: ${{ parameters.destroyTerraform }}
      forceTerraform: ${{ parameters.forceTerraform }}
      planOnly: ${{ eq(variables['Build.Reason'], 'PullRequest') }}
