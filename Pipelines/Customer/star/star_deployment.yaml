trigger:
  branches:
    include:
      - develop
  paths:
    include:
      - 'Components/Customer/**'
      - 'Environments/Customer/star/**'

pr:
  branches:
    include:
      - '*'
  paths:
    include:
      - 'Components/Customer/**'
      - 'Environments/Customer/star/**'

parameters:
  - name: destroyTerraform
    type: boolean
    default: false
  - name: forceTerraform
    type: boolean
    default: false

variables:
  - group: 'GCP-Terraform'

stages:
  - template: ../../../templates/gcp-terraforming.tmpl.yml
    parameters:
      name: 'Terraform_Customer_Star_Dev'
      environment: 'Dev'
      workingDir: '$(System.DefaultWorkingDirectory)/Components/Customer'
      variableDirectory: '$(System.DefaultWorkingDirectory)/Environments/Customer/star/dev'
      azureServiceConnectionForWIF: $(AZURE_SERVICE_CONNECTION_WIF)
      backendGcsBucket: $(GCS_BACKEND_BUCKET)
      backendGcsPrefix: 'Customer/star/dev.tfstate'
      tfPlanName: 'star.dev.tfplan'
      tfDestroy: ${{ parameters.destroyTerraform }}
      forceTerraform: ${{ parameters.forceTerraform }}
      planOnly: ${{ eq(variables['Build.Reason'], 'PullRequest') }}