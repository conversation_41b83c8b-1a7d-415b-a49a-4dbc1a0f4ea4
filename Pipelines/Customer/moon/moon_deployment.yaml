trigger:
  branches:
    include:
      - develop
  paths:
    include:
      - 'Components/Customer/**'
      - 'Environments/Customer/moon/**'

pr:
  branches:
    include:
      - '*'
  paths:
    include:
      - 'Components/Customer/**'
      - 'Environments/Customer/moon/**'

parameters:
  - name: destroyTerraform
    type: boolean
    default: false
  - name: forceTerraform
    type: boolean
    default: false

variables:
  - group: 'GCP-Terraform'

stages:
  - template: ../../../templates/gcp-terraforming.tmpl.yml
    parameters:
      name: 'Terraform_Customer_Moon_Dev'
      environment: 'Dev'
      workingDir: '$(System.DefaultWorkingDirectory)/Components/Customer'
      variableDirectory: '$(System.DefaultWorkingDirectory)/Environments/Customer/moon/dev'
      azureServiceConnectionForWIF: $(AZURE_SERVICE_CONNECTION_WIF)
      backendGcsBucket: $(GCS_BACKEND_BUCKET)
      backendGcsPrefix: 'Customer/moon/dev.tfstate'
      tfPlanName: 'moon.dev.tfplan'
      tfDestroy: ${{ parameters.destroyTerraform }}
      forceTerraform: ${{ parameters.forceTerraform }}
      planOnly: ${{ eq(variables['Build.Reason'], 'PullRequest') }}
