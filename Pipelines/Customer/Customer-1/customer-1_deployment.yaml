trigger:
  branches:
    include:
      - develop
  paths:
    include:
      - 'Components/Customer/**'
      - 'Environments/Customer/Customer-1/**'

pr:
  branches:
    include:
      - '*'
  paths:
    include:
      - 'Components/Customer/**'
      - 'Environments/Customer/Customer-1/**'

parameters:
  - name: destroyTerraform
    type: boolean
    default: false
  - name: forceTerraform
    type: boolean
    default: false

variables:
  - group: 'GCP-Terraform'

stages:
  - template: ../../../templates/gcp-terraforming.tmpl.yml
    parameters:
      name: 'Terraform_Customer_CUAT'
      environment: 'CUAT'
      workingDir: '$(System.DefaultWorkingDirectory)/Components/Customer'
      variableDirectory: '$(System.DefaultWorkingDirectory)/Environments/Customer/Customer-1/cuat'
      azureServiceConnectionForWIF: $(AZURE_SERVICE_CONNECTION_WIF)
      backendGcsBucket: $(GCS_BACKEND_BUCKET)
      backendGcsPrefix: 'Customer/Customer-1/cuat.tfstate'
      tfPlanName: 'customer-1.cuat.tfplan'
      tfDestroy: ${{ parameters.destroyTerraform }}
      forceTerraform: ${{ parameters.forceTerraform }}
      planOnly: ${{ eq(variables['Build.Reason'], 'PullRequest') }}

  - template: ../../../templates/gcp-terraforming.tmpl.yml
    parameters:
      name: 'Terraform_Customer_Live'
      environment: 'Live'
      workingDir: '$(System.DefaultWorkingDirectory)/Components/Customer'
      variableDirectory: '$(System.DefaultWorkingDirectory)/Environments/Customer/Customer-1/live'
      azureServiceConnectionForWIF: $(AZURE_SERVICE_CONNECTION_WIF)
      backendGcsBucket: $(GCS_BACKEND_BUCKET)
      backendGcsPrefix: 'Customer/Customer-1/live.tfstate'
      tfPlanName: 'customer-1.live.tfplan'
      tfDestroy: ${{ parameters.destroyTerraform }}
      forceTerraform: ${{ parameters.forceTerraform }}
      planOnly: ${{ eq(variables['Build.Reason'], 'PullRequest') }}
