trigger:
  branches:
    include:
      - develop
  paths:
    include:
      - 'Components/Internal/hub/**'
      - 'Environments/Internal/hub/**'

pr:
  branches:
    include:
      - '*'
  paths:
    include:
      - 'Components/Internal/hub/**'
      - 'Environments/Internal/hub/**'

parameters:
  - name: destroyTerraform
    type: boolean
    default: false
  - name: forceTerraform
    type: boolean
    default: false

variables:
  - group: 'GCP-Terraform'

stages:
  - template: ../../../templates/gcp-terraforming.tmpl.yml
    parameters:
      name: 'Terraform_Internal_Hub_NonProd'
      environment: 'NonProd'
      workingDir: '$(System.DefaultWorkingDirectory)/Components/Internal/hub'
      variableDirectory: '$(System.DefaultWorkingDirectory)/Environments/Internal/hub/nonprod'
      azureServiceConnectionForWIF: $(AZURE_SERVICE_CONNECTION_WIF)
      backendGcsBucket: $(GCS_BACKEND_BUCKET)
      backendGcsPrefix: 'Internal/hub/nonprod/terraform.tfstate'
      tfPlanName: 'internal.hub.nonprod.tfplan'
      tfDestroy: ${{ parameters.destroyTerraform }}
      forceTerraform: ${{ parameters.forceTerraform }}
      planOnly: ${{ eq(variables['Build.Reason'], 'PullRequest') }}

  - template: ../../../templates/gcp-terraforming.tmpl.yml
    parameters:
      name: 'Terraform_Internal_Hub_Prod'
      environment: 'Prod'
      workingDir: '$(System.DefaultWorkingDirectory)/Components/Internal/hub'
      variableDirectory: '$(System.DefaultWorkingDirectory)/Environments/Internal/hub/prod'
      azureServiceConnectionForWIF: $(AZURE_SERVICE_CONNECTION_WIF)
      backendGcsBucket: $(GCS_BACKEND_BUCKET)
      backendGcsPrefix: 'Internal/hub/prod/terraform.tfstate'
      tfPlanName: 'internal.hub.prod.tfplan'
      tfDestroy: ${{ parameters.destroyTerraform }}
      forceTerraform: ${{ parameters.forceTerraform }}
      planOnly: ${{ eq(variables['Build.Reason'], 'PullRequest') }}