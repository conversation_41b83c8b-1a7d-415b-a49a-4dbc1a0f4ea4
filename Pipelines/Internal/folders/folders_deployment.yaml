trigger:
  branches:
    include:
      - develop
  paths:
    include:
      - 'Components/Internal/folders/**'
      - 'Environments/Internal/folders/**'

pr:
  branches:
    include:
      - '*'
  paths:
    include:
      - 'Components/Internal/folders/**'
      - 'Environments/Internal/folders/**'

parameters:
  - name: destroyTerraform
    type: boolean
    default: false
  - name: forceTerraform
    type: boolean
    default: false

variables:
  - group: 'GCP-Terraform'

stages:
  - template: ../../../templates/gcp-terraforming.tmpl.yml
    parameters:
      name: 'Terraform_Internal_Folders'
      environment: 'Shared'
      workingDir: '$(System.DefaultWorkingDirectory)/Components/Internal/folders'
      variableDirectory: '$(System.DefaultWorkingDirectory)/Environments/Internal/folders/terraform'
      azureServiceConnectionForWIF: $(AZURE_SERVICE_CONNECTION_WIF)
      backendGcsBucket: $(GCS_BACKEND_BUCKET)
      backendGcsPrefix: 'Internal/folders/terraform.tfstate'
      tfPlanName: 'internal.folders.tfplan'
      tfDestroy: ${{ parameters.destroyTerraform }}
      forceTerraform: ${{ parameters.forceTerraform }}
      planOnly: ${{ eq(variables['Build.Reason'], 'PullRequest') }}