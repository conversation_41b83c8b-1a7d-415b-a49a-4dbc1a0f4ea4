trigger:
  branches:
    include:
      - develop
  paths:
    include:
      - 'Components/Internal/groups/**'
      - 'Environments/Internal/groups/**'

pr:
  branches:
    include:
      - '*'
  paths:
    include:
      - 'Components/Internal/groups/**'
      - 'Environments/Internal/groups/**'

parameters:
  - name: destroyTerraform
    type: boolean
    default: false
  - name: forceTerraform
    type: boolean
    default: false

variables:
  - group: 'GCP-Terraform'

stages:
  - template: ../../../templates/gcp-terraforming.tmpl.yml
    parameters:
      name: 'Terraform_Internal_Groups'
      environment: 'Shared'
      workingDir: '$(System.DefaultWorkingDirectory)/Components/Internal/groups'
      variableDirectory: '$(System.DefaultWorkingDirectory)/Environments/Internal/groups/terraform'
      azureServiceConnectionForWIF: $(AZURE_SERVICE_CONNECTION_WIF)
      backendGcsBucket: $(GCS_BACKEND_BUCKET)
      backendGcsPrefix: 'Internal/groups/terraform.tfstate'
      tfPlanName: 'internal.groups.tfplan'
      tfDestroy: ${{ parameters.destroyTerraform }}
      forceTerraform: ${{ parameters.forceTerraform }}
      planOnly: ${{ eq(variables['Build.Reason'], 'PullRequest') }}