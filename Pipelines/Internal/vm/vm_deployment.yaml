trigger:
  branches:
    include:
      - develop
  paths:
    include:
      - 'Components/Internal/vm/**'
      - 'Environments/Internal/vm/**'

pr:
  branches:
    include:
      - '*'
  paths:
    include:
      - 'Components/Internal/vm/**'
      - 'Environments/Internal/vm/**'

parameters:
  - name: destroyTerraform
    type: boolean
    default: false
  - name: forceTerraform
    type: boolean
    default: false

variables:
  - group: 'GCP-Terraform'

stages:
  - template: ../../../templates/gcp-terraforming.tmpl.yml
    parameters:
      name: 'Terraform_Internal_Vm_NonProd'
      environment: 'NonProd'
      workingDir: '$(System.DefaultWorkingDirectory)/Components/Internal/vm'
      variableDirectory: '$(System.DefaultWorkingDirectory)/Environments/Internal/vm/nonprod'
      azureServiceConnectionForWIF: $(AZURE_SERVICE_CONNECTION_WIF)
      backendGcsBucket: $(GCS_BACKEND_BUCKET)
      backendGcsPrefix: 'Internal/vm/terraform.tfstate'
      tfPlanName: 'internal.vm.tfplan'
      tfDestroy: ${{ parameters.destroyTerraform }}
      forceTerraform: ${{ parameters.forceTerraform }}
      planOnly: ${{ eq(variables['Build.Reason'], 'PullRequest') }}