trigger:
  branches:
    include:
      - develop
  paths:
    include:
      - 'Components/Internal/resource_policies/**'
      - 'Environments/Internal/resource_policies/**'

pr:
  branches:
    include:
      - '*'
  paths:
    include:
      - 'Components/Internal/resource_policies/**'
      - 'Environments/Internal/resource_policies/**'

parameters:
  - name: destroyTerraform
    type: boolean
    default: false
  - name: forceTerraform
    type: boolean
    default: false

variables:
  - group: 'GCP-Terraform'

stages:
  - template: ../../../templates/gcp-terraforming.tmpl.yml
    parameters:
      name: 'Terraform_Internal_Resource_Policy_NonProd'
      environment: 'NonProd'
      workingDir: '$(System.DefaultWorkingDirectory)/Components/Internal/resource_policies'
      variableDirectory: '$(System.DefaultWorkingDirectory)/Environments/Internal/resource_policies/nonprod'
      azureServiceConnectionForWIF: $(AZURE_SERVICE_CONNECTION_WIF)
      backendGcsBucket: $(GCS_BACKEND_BUCKET)
      backendGcsPrefix: 'Internal/resource_policies/nonprod/terraform.tfstate'
      tfPlanName: 'internal.resource_policies.nonprod.tfplan'
      tfDestroy: ${{ parameters.destroyTerraform }}
      forceTerraform: ${{ parameters.forceTerraform }}
      planOnly: ${{ eq(variables['Build.Reason'], 'PullRequest') }}
