trigger:
  branches:
    include:
      - develop
  paths:
    include:
      - 'Components/Internal/org_policies/**'
      - 'Environments/Internal/org_policies/**'

pr:
  branches:
    include:
      - '*'
  paths:
    include:
      - 'Components/Internal/org_policies/**'
      - 'Environments/Internal/org_policies/**'

parameters:
  - name: destroyTerraform
    type: boolean
    default: false
  - name: forceTerraform
    type: boolean
    default: false

variables:
  - group: 'GCP-Terraform'

stages:
  - template: ../../../templates/gcp-terraforming.tmpl.yml
    parameters:
      name: 'Terraform_Internal_Org_policies'
      environment: 'Shared'
      workingDir: '$(System.DefaultWorkingDirectory)/Components/Internal/org_policies'
      variableDirectory: '$(System.DefaultWorkingDirectory)/Environments/Internal/org_policies/terraform'
      azureServiceConnectionForWIF: $(AZURE_SERVICE_CONNECTION_WIF)
      backendGcsBucket: $(GCS_BACKEND_BUCKET)
      backendGcsPrefix: 'Internal/org_policies/terraform.tfstate'
      tfPlanName: 'internal.org_policies.tfplan'
      tfDestroy: ${{ parameters.destroyTerraform }}
      forceTerraform: ${{ parameters.forceTerraform }}
      planOnly: ${{ eq(variables['Build.Reason'], 'PullRequest') }}