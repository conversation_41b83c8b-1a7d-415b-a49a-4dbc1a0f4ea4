trigger:
  branches:
    include:
      - develop
  paths:
    include:
      - 'Components/Internal/projects/**'
      - 'Environments/Internal/projects/**'

pr:
  branches:
    include:
      - '*'
  paths:
    include:
      - 'Components/Internal/projects/**'
      - 'Environments/Internal/projects/**'

parameters:
  - name: destroyTerraform
    type: boolean
    default: false
  - name: forceTerraform
    type: boolean
    default: false

variables:
  - group: 'GCP-Terraform'

stages:
  - template: ../../../templates/gcp-terraforming.tmpl.yml
    parameters:
      name: 'Terraform_Internal_Projects_NonProd'
      environment: 'NonProd'
      workingDir: '$(System.DefaultWorkingDirectory)/Components/Internal/projects'
      variableDirectory: '$(System.DefaultWorkingDirectory)/Environments/Internal/projects/nonprod'
      azureServiceConnectionForWIF: $(AZURE_SERVICE_CONNECTION_WIF)
      backendGcsBucket: $(GCS_BACKEND_BUCKET)
      backendGcsPrefix: 'Internal/projects/nonprod/terraform.tfstate'
      tfPlanName: 'internal.projects.nonprod.tfplan'
      tfDestroy: ${{ parameters.destroyTerraform }}
      forceTerraform: ${{ parameters.forceTerraform }}
      planOnly: ${{ eq(variables['Build.Reason'], 'PullRequest') }}

  - template: ../../../templates/gcp-terraforming.tmpl.yml
    parameters:
      name: 'Terraform_Internal_Projects_Prod'
      environment: 'Prod'
      workingDir: '$(System.DefaultWorkingDirectory)/Components/Internal/projects'
      variableDirectory: '$(System.DefaultWorkingDirectory)/Environments/Internal/projects/prod'
      azureServiceConnectionForWIF: $(AZURE_SERVICE_CONNECTION_WIF)
      backendGcsBucket: $(GCS_BACKEND_BUCKET)
      backendGcsPrefix: 'Internal/projects/prod/terraform.tfstate'
      tfPlanName: 'internal.projects.prod.tfplan'
      tfDestroy: ${{ parameters.destroyTerraform }}
      forceTerraform: ${{ parameters.forceTerraform }}
      planOnly: ${{ eq(variables['Build.Reason'], 'PullRequest') }}