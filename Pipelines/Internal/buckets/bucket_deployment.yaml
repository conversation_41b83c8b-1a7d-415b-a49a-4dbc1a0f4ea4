trigger:
  branches:
    include:
      - develop
  paths:
    include:
      - 'Components/Internal/buckets/**'
      - 'Environments/Internal/buckets/**'

pr:
  branches:
    include:
      - '*'
  paths:
    include:
      - 'Components/Internal/buckets/**'
      - 'Environments/Internal/buckets/**'

parameters:
  - name: destroyTerraform
    type: boolean
    default: false
  - name: forceTerraform
    type: boolean
    default: false

variables:
  - group: 'GCP-Terraform'

stages:
  - template: ../../../templates/gcp-terraforming.tmpl.yml
    parameters:
      name: 'Terraform_Internal_Bucket_NonProd'
      environment: 'NonProd'
      workingDir: '$(System.DefaultWorkingDirectory)/Components/Internal/buckets'
      variableDirectory: '$(System.DefaultWorkingDirectory)/Environments/Internal/buckets/nonprod'
      azureServiceConnectionForWIF: $(AZURE_SERVICE_CONNECTION_WIF)
      backendGcsBucket: $(GCS_BACKEND_BUCKET)
      backendGcsPrefix: 'Internal/buckets/nonprod/terraform.tfstate'
      tfPlanName: 'internal.buckets.nonprod.tfplan'
      tfDestroy: ${{ parameters.destroyTerraform }}
      forceTerraform: ${{ parameters.forceTerraform }}
      planOnly: ${{ eq(variables['Build.Reason'], 'PullRequest') }}
