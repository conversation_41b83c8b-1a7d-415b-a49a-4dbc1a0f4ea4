trigger:
  branches:
    include:
      - develop
  paths:
    include:
      - 'Components/Internal/iam/**'
      - 'Environments/Internal/iam/**'

pr:
  branches:
    include:
      - '*'
  paths:
    include:
      - 'Components/Internal/iam/**'
      - 'Environments/Internal/iam/**'

parameters:
  - name: destroyTerraform
    type: boolean
    default: false
  - name: forceTerraform
    type: boolean
    default: false

variables:
  - group: 'GCP-Terraform'

stages:
  - template: ../../../templates/gcp-terraforming.tmpl.yml
    parameters:
      name: 'Terraform_Internal_Iam'
      environment: 'Shared'
      workingDir: '$(System.DefaultWorkingDirectory)/Components/Internal/iam'
      variableDirectory: '$(System.DefaultWorkingDirectory)/Environments/Internal/iam/terraform'
      azureServiceConnectionForWIF: $(AZURE_SERVICE_CONNECTION_WIF)
      backendGcsBucket: $(GCS_BACKEND_BUCKET)
      backendGcsPrefix: 'Internal/iam/terraform.tfstate'
      tfPlanName: 'internal.iam.tfplan'
      tfDestroy: ${{ parameters.destroyTerraform }}
      forceTerraform: ${{ parameters.forceTerraform }}
      planOnly: ${{ eq(variables['Build.Reason'], 'PullRequest') }}