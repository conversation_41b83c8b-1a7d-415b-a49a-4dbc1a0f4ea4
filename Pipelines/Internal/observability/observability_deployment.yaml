trigger:
  branches:
    include:
      - develop
  paths:
    include:
      - 'Components/Internal/observability/**'
      - 'Environments/Internal/observability/**'

pr:
  branches:
    include:
      - '*'
  paths:
    include:
      - 'Components/Internal/observability/**'
      - 'Environments/Internal/observability/**'

parameters:
  - name: destroyTerraform
    type: boolean
    default: false
  - name: forceTerraform
    type: boolean
    default: false

variables:
  - group: 'GCP-Terraform'

stages:
  - template: ../../../templates/gcp-terraforming.tmpl.yml
    parameters:
      name: 'Terraform_Internal_observability_NonProd'
      environment: 'NonProd'
      workingDir: '$(System.DefaultWorkingDirectory)/Components/Internal/observability'
      variableDirectory: '$(System.DefaultWorkingDirectory)/Environments/Internal/observability/nonprod'
      azureServiceConnectionForWIF: $(AZURE_SERVICE_CONNECTION_WIF)
      backendGcsBucket: $(GCS_BACKEND_BUCKET)
      backendGcsPrefix: 'Internal/observability/nonprod/terraform.tfstate'
      tfPlanName: 'internal.observability.nonprod.tfplan'
      tfDestroy: ${{ parameters.destroyTerraform }}
      forceTerraform: ${{ parameters.forceTerraform }}
      planOnly: ${{ eq(variables['Build.Reason'], 'PullRequest') }}

  - template: ../../../templates/gcp-terraforming.tmpl.yml
    parameters:
      name: 'Terraform_Internal_observability_Prod'
      environment: 'Prod'
      workingDir: '$(System.DefaultWorkingDirectory)/Components/Internal/observability'
      variableDirectory: '$(System.DefaultWorkingDirectory)/Environments/Internal/observability/prod'
      azureServiceConnectionForWIF: $(AZURE_SERVICE_CONNECTION_WIF)
      backendGcsBucket: $(GCS_BACKEND_BUCKET)
      backendGcsPrefix: 'Internal/observability/prod/terraform.tfstate'
      tfPlanName: 'internal.observability.prod.tfplan'
      tfDestroy: ${{ parameters.destroyTerraform }}
      forceTerraform: ${{ parameters.forceTerraform }}
      planOnly: ${{ eq(variables['Build.Reason'], 'PullRequest') }}

  - template: ../../../templates/gcp-terraforming.tmpl.yml
    parameters:
      name: 'Terraform_Internal_observability_Shared'
      environment: 'Shared'
      workingDir: '$(System.DefaultWorkingDirectory)/Components/Internal/observability'
      variableDirectory: '$(System.DefaultWorkingDirectory)/Environments/Internal/observability/shared'
      azureServiceConnectionForWIF: $(AZURE_SERVICE_CONNECTION_WIF)
      backendGcsBucket: $(GCS_BACKEND_BUCKET)
      backendGcsPrefix: 'Internal/observability/shared/terraform.tfstate'
      tfPlanName: 'internal.observability.shared.tfplan'
      tfDestroy: ${{ parameters.destroyTerraform }}
      forceTerraform: ${{ parameters.forceTerraform }}
      planOnly: ${{ eq(variables['Build.Reason'], 'PullRequest') }}